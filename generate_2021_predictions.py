#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的2021年股票涨跌预测生成脚本
基于Qdrant向量数据库中的44,408条财务向量数据，使用完整的数学模型架构生成前瞻性预测

使用方法:
    python generate_2021_predictions.py [选项]

示例:
    # 使用默认参数
    python generate_2021_predictions.py
    
    # 自定义参数
    python generate_2021_predictions.py --host localhost --collection financial_vectors --train-start 2012 --train-end 2020

输出文件:
    - predictions_2021.csv: 完整预测结果
    - doc/report.md: 详细分析报告
    - performance_metrics.json: 模型性能指标
"""

import argparse
import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
from tqdm import tqdm

# 导入自定义模块
try:
    from src.data_loader import FinancialDataLoader
    from src.feature_engineer import FeatureEngineer
    from src.labels import generate_trend_labels_with_scores
    from src.math_models import BayesianKDEClassifier, MVNGaussian, fundamental_var, sharpe_ratio, information_ratio
    from src.utils_qdrant import get_qdrant_client, count_with_filter, build_year_filter
except ImportError as e:
    print(f"[错误] 无法导入必要模块: {e}")
    print("请确保所有src模块文件存在且可访问")
    sys.exit(1)


class StockPredictionPipeline:
    """完整的股票预测管线"""
    
    def __init__(self, host: str = 'localhost', http_port: int = 6333, grpc_port: int = 6334, 
                 collection: str = 'financial_vectors'):
        self.host = host
        self.http_port = http_port
        self.grpc_port = grpc_port
        self.collection = collection
        
        # 初始化组件
        self.loader = None
        self.feature_engineer = None
        self.bayes_classifier = None
        self.mvn_model = None
        self.var_model = None
        
        # 性能指标
        self.performance_metrics = {}
        self.processing_times = {}
        
    def initialize_connection(self) -> bool:
        """初始化Qdrant连接"""
        try:
            print(f"[信息] 正在连接Qdrant数据库: {self.host}:{self.http_port} (gRPC {self.grpc_port})")
            self.loader = FinancialDataLoader(
                collection=self.collection, 
                host=self.host,
                http_port=self.http_port, 
                grpc_port=self.grpc_port
            )
            
            # 测试连接
            client = get_qdrant_client(self.host, self.http_port, self.grpc_port)
            total_count = count_with_filter(client, self.collection, None)
            print(f"[信息] 连接成功，向量数据库包含 {total_count:,} 条记录")
            return True
            
        except Exception as e:
            print(f"[错误] 连接Qdrant失败: {e}")
            return False
    
    def load_training_data(self, train_start: int, train_end: int) -> Tuple[bool, Dict]:
        """加载训练数据"""
        start_time = time.time()
        print(f"[信息] 正在加载训练数据 ({train_start}-{train_end})...")
        
        try:
            # 构建年度数据映射
            years = list(range(train_start, train_end + 1))
            year_to_X_meta = {}
            
            print(f"[进度] 按年份加载向量数据...")
            for year in tqdm(years, desc="加载年度数据"):
                X_year, M_year = self.loader.load_vectors_for_year(year)
                if not X_year.empty:
                    year_to_X_meta[year] = (X_year, M_year)
                    print(f"  {year}年: {len(X_year):,} 条记录")
                else:
                    print(f"  {year}年: 无数据")
            
            if not year_to_X_meta:
                print("[错误] 未能加载任何年度数据")
                return False, {}
            
            # 生成训练标签和投影分数
            print("[信息] 基于行业趋势生成训练标签...")
            X_train_raw, y_train, M_train, r_train = generate_trend_labels_with_scores(
                year_to_X_meta, years
            )
            
            if X_train_raw.empty or len(y_train) == 0:
                print("[错误] 无法生成有效的训练标签")
                return False, {}
            
            # 统计信息
            pos_ratio = y_train.mean()
            print(f"[信息] 训练数据生成完成:")
            print(f"  - 样本总数: {len(y_train):,}")
            print(f"  - 正类样本: {y_train.sum():,} ({pos_ratio:.1%})")
            print(f"  - 负类样本: {(~y_train.astype(bool)).sum():,} ({1-pos_ratio:.1%})")
            print(f"  - 特征维度: {X_train_raw.shape[1]}")
            
            self.processing_times['data_loading'] = time.time() - start_time
            
            return True, {
                'X_train_raw': X_train_raw,
                'y_train': y_train,
                'M_train': M_train,
                'r_train': r_train,
                'year_to_X_meta': year_to_X_meta
            }
            
        except Exception as e:
            print(f"[错误] 加载训练数据失败: {e}")
            return False, {}
    
    def engineer_features(self, X_train_raw: pd.DataFrame, M_train: pd.DataFrame) -> Tuple[bool, np.ndarray]:
        """特征工程处理"""
        start_time = time.time()
        print("[信息] 正在进行特征工程...")
        
        try:
            self.feature_engineer = FeatureEngineer()
            
            print("  - 标准化325维财务特征向量")
            print("  - 对行业/市场/交易所/市值进行OneHot编码")
            
            Z_train = self.feature_engineer.fit_transform(X_train_raw, M_train)
            
            print(f"[信息] 特征工程完成:")
            print(f"  - 原始特征维度: {X_train_raw.shape[1]}")
            print(f"  - 工程后特征维度: {Z_train.shape[1]}")
            print(f"  - 特征名称数量: {len(self.feature_engineer.feature_names_)}")
            
            self.processing_times['feature_engineering'] = time.time() - start_time
            return True, Z_train
            
        except Exception as e:
            print(f"[错误] 特征工程失败: {e}")
            return False, None
    
    def train_mathematical_models(self, Z_train: np.ndarray, y_train: pd.Series, r_train: np.ndarray) -> bool:
        """训练所有数学模型"""
        start_time = time.time()
        print("[信息] 正在训练数学模型...")
        
        try:
            # 1. 贝叶斯KDE分类器
            print("  - 训练贝叶斯KDE分类器 (P(涨|财务指标))")
            self.bayes_classifier = BayesianKDEClassifier(n_components=20)
            self.bayes_classifier.fit(Z_train, y_train.values)
            
            print(f"    先验概率: P(涨)={self.bayes_classifier.p_up:.3f}, P(跌)={self.bayes_classifier.p_down:.3f}")
            
            # 2. 多元正态分布模型
            print("  - 训练多元正态分布模型 (密度估计)")
            self.mvn_model = MVNGaussian()
            self.mvn_model.fit(Z_train)
            
            print(f"    特征均值范围: [{np.min(self.mvn_model.mean_):.3f}, {np.max(self.mvn_model.mean_):.3f}]")
            
            # 3. 基本面VaR模型
            print("  - 训练基本面VaR模型 (风险评估)")
            var_alpha = 0.05
            self.var_value, self.var_model = fundamental_var(Z_train, r_train, alpha=var_alpha)
            
            print(f"    VaR({var_alpha*100:.0f}%): {self.var_value:.4f}")
            print(f"    线性模型R²: {self.var_model.score(Z_train, r_train):.3f}")
            
            # 4. 组合指标计算
            sharpe = sharpe_ratio(r_train)
            benchmark = np.median(r_train) * np.ones_like(r_train)
            info_ratio = information_ratio(r_train, benchmark)
            
            print(f"  - 训练期组合指标:")
            print(f"    夏普比率: {sharpe:.3f}")
            print(f"    信息比率: {info_ratio:.3f}")
            
            # 保存性能指标
            self.performance_metrics.update({
                'prior_prob_up': float(self.bayes_classifier.p_up),
                'prior_prob_down': float(self.bayes_classifier.p_down),
                'var_5pct': float(self.var_value),
                'linear_model_r2': float(self.var_model.score(Z_train, r_train)),
                'sharpe_ratio': float(sharpe),
                'information_ratio': float(info_ratio),
                'training_samples': int(len(y_train)),
                'feature_dimensions': int(Z_train.shape[1])
            })
            
            self.processing_times['model_training'] = time.time() - start_time
            print(f"[信息] 数学模型训练完成 (用时 {time.time() - start_time:.1f}秒)")
            return True
            
        except Exception as e:
            print(f"[错误] 模型训练失败: {e}")
            return False
    
    def generate_predictions(self, predict_year: int) -> Tuple[bool, Optional[pd.DataFrame]]:
        """生成2021年预测"""
        start_time = time.time()
        feature_year = predict_year - 1
        print(f"[信息] 正在生成{predict_year}年预测 (使用{feature_year}年特征)...")
        
        try:
            # 加载预测特征
            X_pred_raw, M_pred = self.loader.load_vectors_for_year(feature_year)
            if X_pred_raw.empty:
                print(f"[错误] 无法加载{feature_year}年的特征数据")
                return False, None
            
            print(f"  - 预测样本数: {len(X_pred_raw):,}")
            
            # 特征工程
            Z_pred = self.feature_engineer.transform(X_pred_raw, M_pred)
            
            # 1. 贝叶斯概率预测
            print("  - 计算贝叶斯后验概率 P(涨|x)")
            proba = self.bayes_classifier.predict_proba(Z_pred)[:, 1]
            pred_direction = (proba > 0.5).astype(int)
            
            # 2. MVN密度置信度
            print("  - 计算多元正态密度置信度")
            density = self.mvn_model.pdf(Z_pred)
            
            # 训练集密度分布用于归一化
            Z_train_sample = np.random.choice(len(self.mvn_model.mean_), 
                                            size=min(5000, len(self.mvn_model.mean_)), 
                                            replace=False)
            train_density_sample = self.mvn_model.pdf(
                np.random.multivariate_normal(self.mvn_model.mean_, self.mvn_model.cov_, 
                                            size=len(Z_train_sample))
            )
            
            # 置信度归一化
            density_10pct = np.percentile(train_density_sample, 10)
            density_90pct = np.percentile(train_density_sample, 90)
            confidence = np.clip((density - density_10pct) / (density_90pct - density_10pct + 1e-12), 0, 1)
            
            # 3. 风险评级
            print("  - 计算风险评级")
            risk_grade = np.where(
                density < np.percentile(train_density_sample, 10), '高风险',
                np.where(density < np.percentile(train_density_sample, 30), '中风险', '低风险')
            )
            
            # 4. 组装结果
            results = M_pred.copy()
            results['prob_up'] = proba
            results['pred_direction'] = pred_direction
            results['confidence'] = confidence
            results['risk_grade'] = risk_grade
            results['density_score'] = density
            
            # 添加预测年份标识
            results['predict_year'] = predict_year
            results['feature_year'] = feature_year
            
            print(f"[信息] 预测生成完成:")
            print(f"  - 预测上涨股票: {pred_direction.sum():,} ({pred_direction.mean():.1%})")
            print(f"  - 平均上涨概率: {proba.mean():.3f}")
            print(f"  - 高置信度样本: {(confidence > 0.7).sum():,} ({(confidence > 0.7).mean():.1%})")
            
            self.processing_times['prediction_generation'] = time.time() - start_time
            return True, results
            
        except Exception as e:
            print(f"[错误] 预测生成失败: {e}")
            return False, None

    def analyze_predictions(self, results: pd.DataFrame) -> Dict:
        """分析预测结果"""
        print("[信息] 正在进行预测结果分析...")

        analysis = {}

        # 1. 整体统计
        analysis['overall'] = {
            'total_stocks': len(results),
            'predicted_up': int(results['pred_direction'].sum()),
            'predicted_down': int((~results['pred_direction'].astype(bool)).sum()),
            'avg_prob_up': float(results['prob_up'].mean()),
            'avg_confidence': float(results['confidence'].mean()),
            'high_confidence_count': int((results['confidence'] > 0.7).sum())
        }

        # 2. 行业分析
        if 'industry' in results.columns:
            industry_analysis = results.groupby('industry').agg({
                'pred_direction': ['count', 'sum', 'mean'],
                'prob_up': 'mean',
                'confidence': 'mean'
            }).round(3)
            industry_analysis.columns = ['股票数量', '预测上涨数', '上涨比例', '平均概率', '平均置信度']
            analysis['by_industry'] = industry_analysis.sort_values('上涨比例', ascending=False)

        # 3. 市值分析
        if 'size_bucket' in results.columns:
            size_analysis = results.groupby('size_bucket').agg({
                'pred_direction': ['count', 'sum', 'mean'],
                'prob_up': 'mean',
                'confidence': 'mean'
            }).round(3)
            size_analysis.columns = ['股票数量', '预测上涨数', '上涨比例', '平均概率', '平均置信度']
            analysis['by_size'] = size_analysis.sort_values('上涨比例', ascending=False)

        # 4. 交易所分析
        if 'exchange' in results.columns:
            exchange_analysis = results.groupby('exchange').agg({
                'pred_direction': ['count', 'sum', 'mean'],
                'prob_up': 'mean',
                'confidence': 'mean'
            }).round(3)
            exchange_analysis.columns = ['股票数量', '预测上涨数', '上涨比例', '平均概率', '平均置信度']
            analysis['by_exchange'] = exchange_analysis.sort_values('上涨比例', ascending=False)

        # 5. 风险分析
        risk_analysis = results.groupby('risk_grade').agg({
            'pred_direction': ['count', 'sum', 'mean'],
            'prob_up': 'mean',
            'confidence': 'mean'
        }).round(3)
        risk_analysis.columns = ['股票数量', '预测上涨数', '上涨比例', '平均概率', '平均置信度']
        analysis['by_risk'] = risk_analysis

        # 6. 投资组合建议
        # 高置信度 + 高概率股票
        portfolio_candidates = results[
            (results['confidence'] > 0.6) &
            (results['prob_up'] > 0.6) &
            (results['risk_grade'] != '高风险')
        ].sort_values(['prob_up', 'confidence'], ascending=False)

        analysis['portfolio_recommendation'] = {
            'total_candidates': len(portfolio_candidates),
            'top_50': portfolio_candidates.head(50),
            'top_100': portfolio_candidates.head(100),
            'top_200': portfolio_candidates.head(200)
        }

        print(f"  - 投资组合候选股票: {len(portfolio_candidates):,}")
        print(f"  - 推荐Top50平均概率: {portfolio_candidates.head(50)['prob_up'].mean():.3f}")

        return analysis

    def save_results(self, results: pd.DataFrame, analysis: Dict, output_file: str) -> bool:
        """保存预测结果"""
        try:
            print(f"[信息] 正在保存预测结果到 {output_file}...")

            # 保存CSV
            results.to_csv(output_file, index=False, encoding='utf-8-sig')

            # 保存性能指标
            metrics_file = 'performance_metrics.json'
            with open(metrics_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'performance_metrics': self.performance_metrics,
                    'processing_times': self.processing_times,
                    'analysis_summary': {
                        'total_stocks': analysis['overall']['total_stocks'],
                        'predicted_up_ratio': analysis['overall']['predicted_up'] / analysis['overall']['total_stocks'],
                        'avg_confidence': analysis['overall']['avg_confidence'],
                        'portfolio_candidates': analysis['portfolio_recommendation']['total_candidates']
                    }
                }, f, indent=2, ensure_ascii=False)

            print(f"  - CSV文件: {output_file}")
            print(f"  - 性能指标: {metrics_file}")

            return True

        except Exception as e:
            print(f"[错误] 保存结果失败: {e}")
            return False

    def generate_report(self, results: pd.DataFrame, analysis: Dict, predict_year: int) -> bool:
        """生成详细分析报告"""
        try:
            os.makedirs('doc', exist_ok=True)
            report_path = 'doc/report.md'

            print(f"[信息] 正在生成分析报告 {report_path}...")

            with open(report_path, 'w', encoding='utf-8') as f:
                # 报告标题
                f.write(f'# {predict_year}年股票涨跌预测分析报告\n\n')
                f.write(f'**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')

                # 执行摘要
                f.write('## 执行摘要\n\n')
                f.write(f'本报告基于2010-2020年的{analysis["overall"]["total_stocks"]:,}条财务向量数据，')
                f.write(f'使用完整的数学模型架构对{predict_year}年股票涨跌进行前瞻性预测。\n\n')

                overall = analysis['overall']
                f.write('### 核心发现\n')
                f.write(f'- **预测上涨股票**: {overall["predicted_up"]:,}只 ({overall["predicted_up"]/overall["total_stocks"]:.1%})\n')
                f.write(f'- **平均上涨概率**: {overall["avg_prob_up"]:.3f}\n')
                f.write(f'- **高置信度股票**: {overall["high_confidence_count"]:,}只 ({overall["high_confidence_count"]/overall["total_stocks"]:.1%})\n')
                f.write(f'- **投资组合候选**: {analysis["portfolio_recommendation"]["total_candidates"]:,}只\n\n')

                # 数据与方法
                f.write('## 数据与方法论\n\n')
                f.write('### 数据来源\n')
                f.write('- **时间跨度**: 2010-2020年历史财务数据\n')
                f.write('- **数据规模**: 44,408条财务向量记录\n')
                f.write('- **特征维度**: 325维财务特征向量\n')
                f.write('- **向量存储**: Qdrant向量数据库\n\n')

                f.write('### 数学模型架构\n')
                f.write('1. **贝叶斯KDE分类器**: P(涨|财务指标) = p(财务指标|涨) × P(涨) / p(财务指标)\n')
                f.write('2. **多元正态分布**: 用于密度估计和置信度计算\n')
                f.write('3. **基本面VaR**: 基于线性模型残差的风险评估\n')
                f.write('4. **特征工程**: 325维向量标准化 + 行业/市值OneHot编码\n\n')

                # 模型性能
                f.write('### 模型性能指标\n')
                metrics = self.performance_metrics
                f.write(f'- **训练样本数**: {metrics["training_samples"]:,}\n')
                f.write(f'- **特征维度**: {metrics["feature_dimensions"]}\n')
                f.write(f'- **先验概率**: P(涨)={metrics["prior_prob_up"]:.3f}, P(跌)={metrics["prior_prob_down"]:.3f}\n')
                f.write(f'- **VaR(5%)**: {metrics["var_5pct"]:.4f}\n')
                f.write(f'- **线性模型R²**: {metrics["linear_model_r2"]:.3f}\n')
                f.write(f'- **夏普比率**: {metrics["sharpe_ratio"]:.3f}\n')
                f.write(f'- **信息比率**: {metrics["information_ratio"]:.3f}\n\n')

                # 预测结果分析
                f.write('## 预测结果分析\n\n')

                # Top 50推荐
                f.write('### Top 50 投资推荐\n')
                f.write('*按上涨概率和置信度排序的前50只股票*\n\n')
                top_50 = analysis['portfolio_recommendation']['top_50']
                display_cols = ['ts_code', 'name', 'industry', 'exchange', 'size_bucket',
                              'prob_up', 'confidence', 'pred_direction', 'risk_grade']
                available_cols = [col for col in display_cols if col in top_50.columns]
                f.write(top_50[available_cols].head(50).to_markdown(index=False, floatfmt='.3f'))
                f.write('\n\n')

                # 行业分析
                if 'by_industry' in analysis:
                    f.write('### 行业趋势分析\n')
                    f.write('*各行业平均上涨概率和置信度*\n\n')
                    f.write(analysis['by_industry'].head(20).to_markdown(floatfmt='.3f'))
                    f.write('\n\n')

                # 市值分析
                if 'by_size' in analysis:
                    f.write('### 市值分布分析\n')
                    f.write('*不同市值区间的预测表现*\n\n')
                    f.write(analysis['by_size'].to_markdown(floatfmt='.3f'))
                    f.write('\n\n')

                # 风险分析
                f.write('### 风险等级分析\n')
                f.write('*基于MVN密度的风险分级*\n\n')
                f.write(analysis['by_risk'].to_markdown(floatfmt='.3f'))
                f.write('\n\n')

                # 投资建议
                f.write('## 投资组合建议\n\n')
                f.write('### 构建策略\n')
                f.write('1. **筛选条件**: 置信度>0.6 且 上涨概率>0.6 且 非高风险\n')
                f.write(f'2. **候选数量**: {analysis["portfolio_recommendation"]["total_candidates"]:,}只股票\n')
                f.write('3. **权重分配**: 建议等权重或按置信度加权\n')
                f.write('4. **行业分散**: 避免单一行业过度集中\n\n')

                f.write('### 风险控制\n')
                f.write('- **剔除高风险股票**: 基于MVN密度低于10%分位数\n')
                f.write('- **设置止损**: 建议单只股票最大损失不超过2%\n')
                f.write('- **定期调仓**: 建议每季度根据最新财务数据调整\n')
                f.write('- **分批建仓**: 避免一次性全仓，建议分3-4批建仓\n\n')

                # 局限性说明
                f.write('## 模型局限性与风险提示\n\n')
                f.write('### 数据局限性\n')
                f.write('- **无真实标签**: 当前使用行业趋势伪标签，缺乏2021年真实收益验证\n')
                f.write('- **历史依赖**: 模型基于历史数据，可能无法预测突发事件影响\n')
                f.write('- **数据滞后**: 财务数据存在季度滞后，可能错过短期机会\n\n')

                f.write('### 模型局限性\n')
                f.write('- **KDE过拟合**: 高维空间中KDE可能存在过拟合风险\n')
                f.write('- **正态假设**: MVN模型假设特征服从多元正态分布\n')
                f.write('- **线性VaR**: 基本面VaR使用线性模型，可能低估非线性风险\n\n')

                f.write('### 投资风险提示\n')
                f.write('- **市场风险**: 股票投资存在本金损失风险\n')
                f.write('- **模型风险**: 预测结果仅供参考，不构成投资建议\n')
                f.write('- **流动性风险**: 部分小市值股票可能存在流动性不足\n')
                f.write('- **集中度风险**: 避免过度集中于少数股票或行业\n\n')

                # 技术附录
                f.write('## 技术附录\n\n')
                f.write('### 处理时间统计\n')
                times = self.processing_times
                f.write(f'- **数据加载**: {times.get("data_loading", 0):.1f}秒\n')
                f.write(f'- **特征工程**: {times.get("feature_engineering", 0):.1f}秒\n')
                f.write(f'- **模型训练**: {times.get("model_training", 0):.1f}秒\n')
                f.write(f'- **预测生成**: {times.get("prediction_generation", 0):.1f}秒\n')
                f.write(f'- **总计用时**: {sum(times.values()):.1f}秒\n\n')

                f.write('### 特征工程详情\n')
                f.write('- **原始特征**: 325维财务向量（利润表+资产负债表+现金流量表+财务指标）\n')
                f.write('- **标准化**: StandardScaler标准化数值特征\n')
                f.write('- **分类编码**: OneHot编码行业、市场、交易所、市值分组\n')
                f.write(f'- **最终维度**: {metrics["feature_dimensions"]}维\n\n')

                f.write('---\n')
                f.write('*本报告由股票涨跌预测系统自动生成*\n')

            print(f"  - 分析报告: {report_path}")
            return True

        except Exception as e:
            print(f"[错误] 生成报告失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='完整的2021年股票涨跌预测生成脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python generate_2021_predictions.py
  python generate_2021_predictions.py --train-start 2012 --train-end 2020
  python generate_2021_predictions.py --host localhost --collection financial_vectors
        """
    )

    # Qdrant连接参数
    parser.add_argument('--host', default='localhost', help='Qdrant主机地址')
    parser.add_argument('--http-port', type=int, default=6333, help='Qdrant HTTP端口')
    parser.add_argument('--grpc-port', type=int, default=6334, help='Qdrant gRPC端口')
    parser.add_argument('--collection', default='financial_vectors', help='Qdrant集合名称')

    # 训练参数
    parser.add_argument('--train-start', type=int, default=2010, help='训练开始年份')
    parser.add_argument('--train-end', type=int, default=2020, help='训练结束年份')
    parser.add_argument('--predict-year', type=int, default=2021, help='预测年份')

    # 输出参数
    parser.add_argument('--output', default='predictions_2021.csv', help='输出CSV文件名')

    args = parser.parse_args()

    print("=" * 80)
    print("🚀 2021年股票涨跌预测生成系统")
    print("=" * 80)
    print(f"训练期间: {args.train_start}-{args.train_end}")
    print(f"预测年份: {args.predict_year}")
    print(f"数据源: {args.host}:{args.http_port}/{args.collection}")
    print("=" * 80)

    # 初始化管线
    pipeline = StockPredictionPipeline(
        host=args.host,
        http_port=args.http_port,
        grpc_port=args.grpc_port,
        collection=args.collection
    )

    # 执行完整流程
    total_start_time = time.time()

    try:
        # 1. 初始化连接
        if not pipeline.initialize_connection():
            sys.exit(1)

        # 2. 加载训练数据
        success, train_data = pipeline.load_training_data(args.train_start, args.train_end)
        if not success:
            sys.exit(1)

        # 3. 特征工程
        success, Z_train = pipeline.engineer_features(
            train_data['X_train_raw'],
            train_data['M_train']
        )
        if not success:
            sys.exit(1)

        # 4. 训练数学模型
        success = pipeline.train_mathematical_models(
            Z_train,
            train_data['y_train'],
            train_data['r_train']
        )
        if not success:
            sys.exit(1)

        # 5. 生成预测
        success, results = pipeline.generate_predictions(args.predict_year)
        if not success:
            sys.exit(1)

        # 6. 分析结果
        analysis = pipeline.analyze_predictions(results)

        # 7. 保存结果
        success = pipeline.save_results(results, analysis, args.output)
        if not success:
            sys.exit(1)

        # 8. 生成报告
        success = pipeline.generate_report(results, analysis, args.predict_year)
        if not success:
            sys.exit(1)

        # 完成总结
        total_time = time.time() - total_start_time
        print("\n" + "=" * 80)
        print("✅ 预测生成完成!")
        print("=" * 80)
        print(f"📊 预测股票数量: {len(results):,}")
        print(f"📈 预测上涨股票: {results['pred_direction'].sum():,} ({results['pred_direction'].mean():.1%})")
        print(f"🎯 投资组合候选: {analysis['portfolio_recommendation']['total_candidates']:,}")
        print(f"⏱️  总计用时: {total_time:.1f}秒")
        print(f"📁 输出文件:")
        print(f"   - {args.output}")
        print(f"   - doc/report.md")
        print(f"   - performance_metrics.json")
        print("=" * 80)

    except KeyboardInterrupt:
        print("\n[信息] 用户中断执行")
        sys.exit(0)
    except Exception as e:
        print(f"\n[错误] 执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
