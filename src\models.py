# -*- coding: utf-8 -*-
"""
Simple classification model for up/down prediction using engineered features.
For demo purpose (no prices), we simulate labels using industry median shifts.
In production, replace labels with actual next-year returns sign.
"""
from typing import Dict, <PERSON>ple
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, roc_auc_score


class UpDownClassifier:
    def __init__(self):
        self.model = RandomForestClassifier(n_estimators=400, max_depth=16, random_state=42, class_weight='balanced_subsample')

    def fit(self, X: np.ndarray, y: np.ndarray) -> Dict:
        X_tr, X_te, y_tr, y_te = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
        self.model.fit(X_tr, y_tr)
        pr = self.model.predict_proba(X_te)[:,1]
        pd = (pr>0.5).astype(int)
        metrics = {
            'acc': float(accuracy_score(y_te, pd)),
            'prec': float(precision_score(y_te, pd, zero_division=0)),
            'rec': float(recall_score(y_te, pd, zero_division=0)),
            'auc': float(roc_auc_score(y_te, pr)) if len(np.unique(y_te))>1 else None,
        }
        return metrics

    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        return self.model.predict_proba(X)

