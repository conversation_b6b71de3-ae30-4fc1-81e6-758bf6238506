# -*- coding: utf-8 -*-
"""
Mathematical models per analysis.md:
- Bayesian classifier with KDE likelihood
- Multivariate normal density estimation
- Fundamental VaR via linear model residual quantiles
- Portfolio Sharpe & Information Ratio utilities
"""
from typing import Tuple, Dict
import numpy as np
from scipy.stats import gaussian_kde, multivariate_normal
from sklearn.linear_model import LinearRegression
from sklearn.decomposition import PCA
from sklearn.ensemble import RandomForestClassifier


class BayesianKDEClassifier:
    """稳健的贝叶斯分类器，结合KDE和随机森林备用方案"""
    def __init__(self, n_components=20, use_rf_backup=True):
        self.p_up = None
        self.p_down = None
        self.pca = PCA(n_components=n_components, random_state=42)
        self.n_components = n_components
        self.use_rf_backup = use_rf_backup
        self.rf_model = None
        self.use_backup = False

    def fit(self, X: np.ndarray, y: np.ndarray):
        X = np.asarray(X)
        y = np.asarray(y).astype(int)

        self.p_up = float(np.mean(y))
        self.p_down = 1.0 - self.p_up

        # 降维处理
        print(f"    降维: {X.shape[1]}维 -> {self.n_components}维")
        X_reduced = self.pca.fit_transform(X)

        # 尝试KDE，如果失败则使用随机森林
        try:
            X_up = X_reduced[y == 1].T
            X_dn = X_reduced[y == 0].T

            if X_up.size == 0 or X_dn.size == 0:
                raise ValueError('Both classes required')

            # 检查样本数是否足够
            if X_up.shape[1] < 50 or X_dn.shape[1] < 50:
                print(f"    样本数不足，使用随机森林备用方案")
                self.use_backup = True
            else:
                self.kde_up = gaussian_kde(X_up, bw_method='silverman')
                self.kde_down = gaussian_kde(X_dn, bw_method='silverman')
                print(f"    KDE训练成功")

        except Exception as e:
            print(f"    KDE训练失败: {e}，使用随机森林备用方案")
            self.use_backup = True

        # 训练随机森林备用模型
        if self.use_backup or self.use_rf_backup:
            print(f"    训练随机森林备用模型")
            self.rf_model = RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            )
            self.rf_model.fit(X_reduced, y)

        return self

    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        X = np.asarray(X)
        X_reduced = self.pca.transform(X)

        if self.use_backup or not hasattr(self, 'kde_up'):
            # 使用随机森林
            return self.rf_model.predict_proba(X_reduced)

        try:
            # 尝试使用KDE
            p_x_up = self.kde_up(X_reduced.T)
            p_x_dn = self.kde_down(X_reduced.T)

            # 检查是否有异常值
            if np.any(np.isnan(p_x_up)) or np.any(np.isnan(p_x_dn)) or np.any(p_x_up <= 0) or np.any(p_x_dn <= 0):
                print("    KDE预测出现异常值，切换到随机森林")
                return self.rf_model.predict_proba(X_reduced)

            # 对数空间计算避免下溢
            log_num = np.log(p_x_up + 1e-300) + np.log(self.p_up)
            log_den_up = np.log(p_x_up + 1e-300) + np.log(self.p_up)
            log_den_dn = np.log(p_x_dn + 1e-300) + np.log(self.p_down)

            # 使用logsumexp技巧
            max_log = np.maximum(log_den_up, log_den_dn)
            log_den = max_log + np.log(np.exp(log_den_up - max_log) + np.exp(log_den_dn - max_log))

            log_post = log_num - log_den
            post = np.exp(np.clip(log_post, -50, 50))  # 防止溢出

            # 检查结果合理性
            if np.all(post < 1e-10) or np.all(post > 1-1e-10):
                print("    KDE预测结果异常，切换到随机森林")
                return self.rf_model.predict_proba(X_reduced)

            return np.vstack([1 - post, post]).T

        except Exception as e:
            print(f"    KDE预测失败: {e}，使用随机森林")
            return self.rf_model.predict_proba(X_reduced)


class MVNGaussian:
    """Multivariate normal density estimation for features (for diagnostics)."""
    def __init__(self):
        self.mean_ = None
        self.cov_ = None
        self.mvn = None

    def fit(self, X: np.ndarray):
        self.mean_ = X.mean(axis=0)
        self.cov_ = np.cov(X.T)
        # Regularize cov if needed
        eps = 1e-6
        self.cov_ = self.cov_ + eps * np.eye(self.cov_.shape[0])
        self.mvn = multivariate_normal(mean=self.mean_, cov=self.cov_)
        return self

    def pdf(self, X: np.ndarray) -> np.ndarray:
        return self.mvn.pdf(X)


def fundamental_var(X: np.ndarray, r: np.ndarray, alpha: float = 0.05) -> Tuple[float, LinearRegression]:
    """
    Fit linear model r = f(X) + eps; VaR_alpha = quantile(eps, alpha)
    """
    lr = LinearRegression()
    lr.fit(X, r)
    resid = r - lr.predict(X)
    var = float(np.quantile(resid, alpha))
    return var, lr


def sharpe_ratio(returns: np.ndarray, rf: float = 0.03) -> float:
    ex = returns - rf / 252.0  # daily approximation if daily returns provided
    mu = np.mean(ex)
    sig = np.std(ex) + 1e-12
    return float(mu / sig)


def information_ratio(returns: np.ndarray, benchmark: np.ndarray) -> float:
    diff = returns - benchmark
    mu = np.mean(diff)
    te = np.std(diff) + 1e-12
    return float(mu / te)

