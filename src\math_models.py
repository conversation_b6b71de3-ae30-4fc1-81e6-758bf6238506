# -*- coding: utf-8 -*-
"""
Mathematical models per analysis.md:
- Bayesian classifier with KDE likelihood
- Multivariate normal density estimation
- Fundamental VaR via linear model residual quantiles
- Portfolio Sharpe & Information Ratio utilities
"""
from typing import Tuple, Dict
import numpy as np
from scipy.stats import gaussian_kde, multivariate_normal
from sklearn.linear_model import LinearRegression


class BayesianKDEClassifier:
    """P(Up|x) ~ p(x|Up) P(Up) / (p(x|Up) P(Up) + p(x|Down) P(Down)) using KDE."""
    def __init__(self, bw_method='scott'):
        self.kde_up = None
        self.kde_down = None
        self.p_up = None
        self.p_down = None
        self.bw_method = bw_method

    def fit(self, X: np.ndarray, y: np.ndarray):
        X = np.asarray(X)
        y = np.asarray(y).astype(int)
        X_up = X[y == 1].T
        X_dn = X[y == 0].T
        if X_up.size == 0 or X_dn.size == 0:
            raise ValueError('Both classes required for BayesianKDEClassifier')
        self.kde_up = gaussian_kde(X_up, bw_method=self.bw_method)
        self.kde_down = gaussian_kde(X_dn, bw_method=self.bw_method)
        self.p_up = float(np.mean(y))
        self.p_down = 1.0 - self.p_up
        return self

    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        X = np.asarray(X)
        p_x_up = self.kde_up(X.T)
        p_x_dn = self.kde_down(X.T)
        num = p_x_up * self.p_up
        den = num + p_x_dn * self.p_down + 1e-12
        post = num / den
        return np.vstack([1 - post, post]).T


class MVNGaussian:
    """Multivariate normal density estimation for features (for diagnostics)."""
    def __init__(self):
        self.mean_ = None
        self.cov_ = None
        self.mvn = None

    def fit(self, X: np.ndarray):
        self.mean_ = X.mean(axis=0)
        self.cov_ = np.cov(X.T)
        # Regularize cov if needed
        eps = 1e-6
        self.cov_ = self.cov_ + eps * np.eye(self.cov_.shape[0])
        self.mvn = multivariate_normal(mean=self.mean_, cov=self.cov_)
        return self

    def pdf(self, X: np.ndarray) -> np.ndarray:
        return self.mvn.pdf(X)


def fundamental_var(X: np.ndarray, r: np.ndarray, alpha: float = 0.05) -> Tuple[float, LinearRegression]:
    """
    Fit linear model r = f(X) + eps; VaR_alpha = quantile(eps, alpha)
    """
    lr = LinearRegression()
    lr.fit(X, r)
    resid = r - lr.predict(X)
    var = float(np.quantile(resid, alpha))
    return var, lr


def sharpe_ratio(returns: np.ndarray, rf: float = 0.03) -> float:
    ex = returns - rf / 252.0  # daily approximation if daily returns provided
    mu = np.mean(ex)
    sig = np.std(ex) + 1e-12
    return float(mu / sig)


def information_ratio(returns: np.ndarray, benchmark: np.ndarray) -> float:
    diff = returns - benchmark
    mu = np.mean(diff)
    te = np.std(diff) + 1e-12
    return float(mu / te)

