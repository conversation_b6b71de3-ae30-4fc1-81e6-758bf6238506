#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vectorize financial data and store embeddings into a local Qdrant vector DB.

- Source data: docs_markdown/*.md (47 files converted from Excel; 2010-2020)
- Feature space: build wide tabular features from 4 statements, add engineered
  ratios and simple time-series features, then reduce to 325-D with IncrementalPCA
- Storage: Qdrant collection with 325-dim cosine vectors; payload includes
  filters like year/industry/market/size bucket

Usage examples:
  # 1) Install dependencies
  pip install -U pandas numpy tqdm scikit-learn qdrant-client

  # 2) Run end-to-end (fit scaler/PCA if not exists, then upsert to Qdrant)
  python scripts/vectorize_to_qdrant.py \
      --docs-dir docs_markdown \
      --collection financial_vectors \
      --host localhost --http-port 6333 --grpc-port 6334 \
      --batch-size 800 --recreate

  # 3) Incremental update (reuse saved scaler/PCA)
  python scripts/vectorize_to_qdrant.py --resume

Notes:
- The script uses up to 3 streaming passes (collect feature names, fit scaler/PCA,
  transform+upsert) to handle ~48k rows efficiently.
- It saves fitted StandardScaler & IncrementalPCA under ./.artifacts
"""

from __future__ import annotations
import os
import re
import json
import time
import math
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Iterable

import numpy as np
import pandas as pd
from tqdm import tqdm
import uuid

from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import IncrementalPCA
from sklearn.utils import murmurhash3_32

from qdrant_client import QdrantClient
from qdrant_client.http import models as qmodels

ARTIFACT_DIR = Path('.artifacts')
ARTIFACT_DIR.mkdir(exist_ok=True)
SCALER_PATH = ARTIFACT_DIR / 'std_scaler.joblib.npy'
PCA_PATH = ARTIFACT_DIR / 'ipca_325.joblib.npz'

logger = logging.getLogger("vectorize_to_qdrant")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s'
)

# -------------------------------
# Helpers for Markdown -> DataFrame
# -------------------------------

def _read_first_markdown_table(md_path: Path) -> pd.DataFrame:
    """Parse the first GitHub-style pipe markdown table into a DataFrame.
    Only the first table is considered. Columns are kept as strings, data coerced later.
    """
    lines = md_path.read_text(encoding='utf-8').splitlines()
    table_started = False
    header = None
    rows: List[List[str]] = []
    for i, ln in enumerate(lines):
        if '|' in ln and '---' in ln and not table_started:
            # Next line should be header separator; header is previous line
            # Find header line above
            j = i - 1
            while j >= 0 and '|' not in lines[j]:
                j -= 1
            if j >= 0:
                header = [c.strip() for c in lines[j].split('|')[1:-1]]
                table_started = True
            continue
        if table_started:
            if '|' in ln:
                cells = [c.strip() for c in ln.split('|')[1:-1]]
                if header and len(cells) == len(header):
                    rows.append(cells)
                else:
                    # likely table end
                    break
            else:
                break
    if header is None:
        return pd.DataFrame()
    df = pd.DataFrame(rows, columns=header)
    # Coerce types (attempt numeric where possible)
    for c in df.columns:
        df[c] = pd.to_numeric(df[c], errors='ignore')
    return df

# -------------------------------
# Data loading per year
# -------------------------------

def load_year_data(docs_dir: Path, year: int) -> Dict[str, pd.DataFrame]:
    """Load 4 statement tables for a given year (if present)."""
    name_map = {
        'profit': f"{year}利润表.md",
        'balance': f"{year}资产负债表.md",
        'cashflow': f"{year}现金流量表.md",
        'ratios': f"{year}财务指标表.md",
    }
    out: Dict[str, pd.DataFrame] = {}
    for k, fname in name_map.items():
        fp = docs_dir / fname
        if fp.exists():
            out[k] = _read_first_markdown_table(fp)
        else:
            out[k] = pd.DataFrame()
    return out


def load_auxiliary_maps(docs_dir: Path) -> Tuple[pd.DataFrame, Dict[str, Dict[str, str]]]:
    """Load 股票列表.md to map ts_code -> name, industry, market, exchange."""
    stock_fp = docs_dir / '股票列表.md'
    stock_df = _read_first_markdown_table(stock_fp) if stock_fp.exists() else pd.DataFrame()
    mapping: Dict[str, Dict[str, str]] = {}
    if not stock_df.empty and 'ts_code' in stock_df.columns:
        for _, r in stock_df.iterrows():
            mapping[str(r['ts_code'])] = {
                'name': str(r.get('name', '')),
                'industry': str(r.get('industry', '')),
                'market': str(r.get('market', '')),
                'exchange': str(r.get('exchange', '')),
            }
    return stock_df, mapping

# -------------------------------
# Feature engineering registry
# -------------------------------

def numeric_columns(df: pd.DataFrame) -> List[str]:
    return [c for c in df.columns if c != 'ts_code' and pd.api.types.is_numeric_dtype(df[c])]


def prefixed_columns(df: pd.DataFrame, prefix: str) -> List[str]:
    return [f"{prefix}__{c}" for c in numeric_columns(df)]


def build_feature_name_set(year_data: Dict[str, pd.DataFrame]) -> List[str]:
    """Collect numeric columns across 4 tables with prefixes to avoid collision.
    Add engineered/time-series feature names template (actual values later)."""
    cols: List[str] = []
    for key, prefix in [('profit', 'inc'), ('balance', 'bal'), ('cashflow', 'cf'), ('ratios', 'rat')]:
        df = year_data.get(key, pd.DataFrame())
        if not df.empty:
            cols.extend(prefixed_columns(df, prefix))
    # Engineered features (template columns)
    eng_cols = [
        'eng__roe', 'eng__roa', 'eng__gross_margin', 'eng__net_margin',
        'eng__current_ratio', 'eng__quick_ratio', 'eng__debt_ratio',
        'eng__asset_turnover', 'eng__inventory_turnover', 'eng__receivables_turnover',
        'ts__revenue_yoy', 'ts__net_income_yoy', 'ts__assets_yoy'
    ]
    # Deduplicate while preserving order
    seen = set()
    ordered = []
    for c in cols + eng_cols:
        if c not in seen:
            seen.add(c)
            ordered.append(c)
    return ordered


def compute_engineered(row_maps: Dict[str, float]) -> Dict[str, float]:
    """Compute engineered ratios from available fields in row_maps (prefixed keys)."""
    v = row_maps
    out = {}
    # Pull helpful fields if exist
    revenue = v.get('inc__revenue') or v.get('rat__revenue_ps')
    oper_cost = v.get('inc__oper_cost')
    n_income = v.get('inc__n_income') or v.get('rat__profit_dedt')
    total_assets = v.get('bal__total_assets')
    total_liab = v.get('bal__total_liab')
    equity = v.get('bal__total_hldr_eqy_exc_min_int')
    cur_assets = v.get('bal__total_cur_assets')
    cur_liab = v.get('bal__total_cur_liab')
    inventories = v.get('bal__inventories')
    accounts_receiv = v.get('bal__accounts_receiv') or v.get('bal__accounts_receivable')
    ebit = v.get('inc__ebit') or v.get('rat__ebit')

    def safe_div(a, b):
        try:
            if a is None or b in (None, 0):
                return np.nan
            return float(a) / float(b)
        except Exception:
            return np.nan

    out['eng__roe'] = safe_div(n_income, equity)
    out['eng__roa'] = safe_div(n_income, total_assets)
    out['eng__gross_margin'] = safe_div((revenue - oper_cost) if (revenue is not None and oper_cost is not None) else np.nan, revenue)
    out['eng__net_margin'] = safe_div(n_income, revenue)
    out['eng__current_ratio'] = safe_div(cur_assets, cur_liab)
    out['eng__quick_ratio'] = safe_div((cur_assets - inventories) if (cur_assets is not None and inventories is not None) else np.nan, cur_liab)
    out['eng__debt_ratio'] = safe_div(total_liab, total_assets)
    out['eng__asset_turnover'] = safe_div(revenue, total_assets)
    out['eng__inventory_turnover'] = safe_div(oper_cost, inventories)
    out['eng__receivables_turnover'] = safe_div(revenue, accounts_receiv)
    return out


# -------------------------------
# Vectorization pipeline
# -------------------------------

class Vectorizer:
    def __init__(self, docs_dir: Path, target_dim: int = 325, batch_size: int = 800):
        self.docs_dir = docs_dir
        self.target_dim = target_dim
        self.batch_size = batch_size
        self.scaler = StandardScaler(with_mean=True, with_std=True)
        self.ipca = IncrementalPCA(n_components=target_dim, batch_size=batch_size)
        self.stock_map_df, self.stock_map = load_auxiliary_maps(docs_dir)
        self.feature_names: List[str] = []
        # ts_code -> {year: raw_minimal_fields} for time-series features
        self.ts_index: Dict[str, Dict[int, Dict[str, float]]] = {}

    def _year_iter(self) -> Iterable[int]:
        # infer from files
        years = set()
        for p in self.docs_dir.glob("*利润表.md"):
            try:
                y = int(p.name[:4])
                years.add(y)
            except Exception:
                pass
        return sorted(years)

    def _row_maps_iter(self) -> Iterable[Tuple[str, int, Dict[str, float]]]:
        """Stream per (ts_code, year) merged row maps from 4 tables with prefixed keys."""
        years = self._year_iter()
        for year in years:
            data = load_year_data(self.docs_dir, year)
            # Build dict of ts_code -> maps
            merged: Dict[str, Dict[str, float]] = {}
            for key, prefix in [('profit','inc'), ('balance','bal'), ('cashflow','cf'), ('ratios','rat')]:
                df = data[key]
                if df.empty or 'ts_code' not in df.columns:
                    continue
                # Coerce ts_code to str
                df['ts_code'] = df['ts_code'].astype(str)
                num_cols = numeric_columns(df)
                for _, r in df.iterrows():
                    code = r['ts_code']
                    mp = merged.setdefault(code, {})
                    for c in num_cols:
                        val = r[c]
                        if pd.notna(val):
                            mp[f"{prefix}__{c}"] = float(val)
            # Compute engineered per code
            for code, mp in merged.items():
                mp.update(compute_engineered(mp))
                # cache minimal ts inputs for time-series
                self.ts_index.setdefault(code, {})[year] = {
                    'revenue': mp.get('inc__revenue'),
                    'n_income': mp.get('inc__n_income'),
                    'total_assets': mp.get('bal__total_assets')
                }
            # yield
            for code, mp in merged.items():
                yield code, year, mp

    def build_feature_registry(self):
        # Single year may miss some columns; union across years
        seen: List[str] = []
        for _, _, mp in self._row_maps_iter():
            for k in mp.keys():
                if k not in seen:
                    seen.append(k)
        # Add time-series engineered names
        ts_names = ['ts__revenue_yoy', 'ts__net_income_yoy', 'ts__assets_yoy']
        for n in ts_names:
            if n not in seen:
                seen.append(n)
        self.feature_names = seen
        logger.info(f"Feature registry built with {len(self.feature_names)} columns before PCA")

    def _vector_from_maps(self, code: str, year: int, mp: Dict[str, float]) -> np.ndarray:
        vec = np.empty(len(self.feature_names), dtype=np.float32)
        vec.fill(np.nan)
        # Fill raw & engineered
        for i, name in enumerate(self.feature_names):
            if name.startswith('ts__'):
                # compute on the fly
                prev = self.ts_index.get(code, {}).get(year - 1, {})
                cur = self.ts_index.get(code, {}).get(year, {})
                if name == 'ts__revenue_yoy':
                    a = cur.get('revenue')
                    b = prev.get('revenue') if prev else None
                    vec[i] = np.nan if (a is None or not b) else (a - b) / b
                elif name == 'ts__net_income_yoy':
                    a = cur.get('n_income')
                    b = prev.get('n_income') if prev else None
                    vec[i] = np.nan if (a is None or not b) else (a - b) / b
                elif name == 'ts__assets_yoy':
                    a = cur.get('total_assets')
                    b = prev.get('total_assets') if prev else None
                    vec[i] = np.nan if (a is None or not b) else (a - b) / b
                else:
                    vec[i] = np.nan
            else:
                val = mp.get(name)
                vec[i] = np.nan if val is None else float(val)
        # Missing -> impute with 0 (after scaling this becomes mean-impute)
        vec = np.nan_to_num(vec, nan=0.0, posinf=0.0, neginf=0.0)
        return vec

    def fit_scaler_and_pca(self):
        # Pass 1: build registry
        self.build_feature_registry()
        # Pass 2: partial_fit scaler
        batch = []
        for code, year, mp in tqdm(self._row_maps_iter(), desc='Fitting scaler', unit='rows'):
            batch.append(self._vector_from_maps(code, year, mp))
            if len(batch) >= self.batch_size:
                X = np.vstack(batch)
                self.scaler.partial_fit(X)
                batch.clear()
        if batch:
            self.scaler.partial_fit(np.vstack(batch))
        # Persist scaler
        np.save(SCALER_PATH, {
            'mean_': self.scaler.mean_,
            'scale_': self.scaler.scale_,
            'n_features_in_': len(self.feature_names)
        }, allow_pickle=True)
        # Pass 3: fit IPCA on scaled features
        batch = []
        for code, year, mp in tqdm(self._row_maps_iter(), desc='Fitting IPCA', unit='rows'):
            x = self._vector_from_maps(code, year, mp)
            x = (x - self.scaler.mean_) / (self.scaler.scale_ + 1e-12)
            batch.append(x.astype(np.float32))
            if len(batch) >= self.batch_size:
                self.ipca.partial_fit(np.vstack(batch))
                batch.clear()
        if batch:
            self.ipca.partial_fit(np.vstack(batch))
        # Save IPCA
        np.savez_compressed(PCA_PATH, components_=self.ipca.components_, mean_=self.ipca.mean_)
        logger.info("Scaler and IPCA fitted and saved")

    def load_scaler_and_pca(self):
        if SCALER_PATH.exists():
            obj = np.load(SCALER_PATH, allow_pickle=True).item()
            self.scaler.mean_ = obj['mean_']
            self.scaler.scale_ = obj['scale_']
        if PCA_PATH.exists():
            obj = np.load(PCA_PATH)
            self.ipca.components_ = obj['components_']
            self.ipca.mean_ = obj['mean_']
        if not hasattr(self.scaler, 'mean_'):
            raise RuntimeError('Scaler not fitted; run without --resume to fit')
        if not hasattr(self.ipca, 'components_'):
            raise RuntimeError('IPCA not fitted; run without --resume to fit')

    def vectors_iter(self) -> Iterable[Tuple[str, int, np.ndarray]]:
        for code, year, mp in self._row_maps_iter():
            x = self._vector_from_maps(code, year, mp)
            x = (x - self.scaler.mean_) / (self.scaler.scale_ + 1e-12)
            z = self.ipca.transform(x.reshape(1, -1)).astype(np.float32)[0]
            yield code, year, z

    def company_payload(self, code: str, year: int, mp: Dict[str, float]) -> Dict:
        meta = self.stock_map.get(code, {})
        # size bucket via total_assets quantile per year could be expensive; use rough bucket
        total_assets = self.ts_index.get(code, {}).get(year, {}).get('total_assets')
        size_bucket = 'unknown'
        if total_assets is not None:
            try:
                a = float(total_assets)
                size_bucket = 'small' if a < 1e9 else 'mid' if a < 1e10 else 'large'
            except Exception:
                pass
        payload = {
            'ts_code': code,
            'year': int(year),
            'name': meta.get('name',''),
            'industry': meta.get('industry',''),
            'market': meta.get('market',''),
            'exchange': meta.get('exchange',''),
            'size_bucket': size_bucket,
        }
        # Add a few key engineered fields for filtering/inspection
        for k in ['eng__roe','eng__roa','eng__gross_margin','eng__debt_ratio','eng__current_ratio','ts__revenue_yoy']:
            val = mp.get(k)
            if val is not None and not (isinstance(val, float) and (math.isnan(val) or math.isinf(val))):
                payload[k] = float(val)
        return payload

# -------------------------------
# Qdrant integration
# -------------------------------

def ensure_collection(client: QdrantClient, name: str, dim: int, recreate: bool = False):
    if recreate and name in [c.name for c in client.get_collections().collections]:
        client.delete_collection(name)
    if name not in [c.name for c in client.get_collections().collections]:
        client.create_collection(
            collection_name=name,
            vectors_config=qmodels.VectorParams(size=dim, distance=qmodels.Distance.COSINE),
            hnsw_config=qmodels.HnswConfigDiff(m=16, ef_construct=200),
            optimizers_config=qmodels.OptimizersConfigDiff(default_segment_number=2),
        )
        # Payload indexes for fast filtering
        for field, ftype in [
            ('year', qmodels.PayloadSchemaType.INTEGER),
            ('industry', qmodels.PayloadSchemaType.KEYWORD),
            ('market', qmodels.PayloadSchemaType.KEYWORD),
            ('exchange', qmodels.PayloadSchemaType.KEYWORD),
            ('size_bucket', qmodels.PayloadSchemaType.KEYWORD),
            ('ts_code', qmodels.PayloadSchemaType.KEYWORD),
        ]:
            try:
                client.create_payload_index(name, field_name=field, field_schema=ftype)
            except Exception:
                pass
        logger.info(f"Created collection '{name}' with dim={dim}, cosine distance")
    else:
        logger.info(f"Using existing collection '{name}'")


def upsert_batches(client: QdrantClient, collection: str, vec: Vectorizer, batch_size: int):
    t0 = time.time()
    cnt = 0
    points: List[qmodels.PointStruct] = []
    for code, year, emb in tqdm(vec.vectors_iter(), desc='Upserting to Qdrant', unit='vec'):
        # Build payload using latest available merged maps for that code/year
        mp = vec.ts_index.get(code, {}).get(year, {})
        # Caution: mp here includes only minimal time-series; enrich via recompute
        # Re-merge maps quickly (cheap for payload fields)
        payload = {
            'ts_code': code,
            'year': int(year),
            **vec.stock_map.get(code, {}),
        }
        # Add size bucket from earlier helper
        total_assets = vec.ts_index.get(code, {}).get(year, {}).get('total_assets')
        if total_assets is not None:
            try:
                a = float(total_assets)
                payload['size_bucket'] = 'small' if a < 1e9 else 'mid' if a < 1e10 else 'large'
            except Exception:
                pass
        # Use stable integer ID for gRPC compatibility; keep business key in payload
        pid_str = f"{code}_{year}"
        int_id = int(murmurhash3_32(pid_str, positive=True))
        payload['point_key'] = pid_str
        points.append(qmodels.PointStruct(id=int_id, vector=emb.tolist(), payload=payload))
        if len(points) >= batch_size:
            client.upsert(collection_name=collection, points=points, wait=True)
            cnt += len(points)
            points.clear()
    if points:
        client.upsert(collection_name=collection, points=points, wait=True)
        cnt += len(points)
    dt = time.time() - t0
    logger.info(f"Upserted {cnt} vectors in {dt:.1f}s ({cnt/dt:.1f} vec/s)")

# -------------------------------
# Main
# -------------------------------

def main():
    ap = argparse.ArgumentParser(description='Vectorize financial data and store into Qdrant')
    ap.add_argument('--docs-dir', default='docs_markdown', help='Markdown directory')
    ap.add_argument('--collection', default='financial_vectors', help='Qdrant collection name')
    ap.add_argument('--host', default='localhost')
    ap.add_argument('--http-port', type=int, default=6333)
    ap.add_argument('--grpc-port', type=int, default=6334)
    ap.add_argument('--batch-size', type=int, default=800)
    ap.add_argument('--recreate', action='store_true', help='Drop & recreate collection')
    ap.add_argument('--resume', action='store_true', help='Reuse saved scaler/PCA (no refit)')
    args = ap.parse_args()

    docs_dir = Path(args.docs_dir)
    if not docs_dir.exists():
        raise FileNotFoundError(f"docs dir not found: {docs_dir}")

    vec = Vectorizer(docs_dir=docs_dir, target_dim=325, batch_size=args.batch_size)

    if args.resume and SCALER_PATH.exists() and PCA_PATH.exists():
        logger.info('Loading saved scaler/IPCA...')
        vec.load_scaler_and_pca()
    else:
        logger.info('Fitting scaler and IPCA from data (one-time pass) ...')
        vec.fit_scaler_and_pca()

    client = QdrantClient(host=args.host, port=args.http_port, grpc_port=args.grpc_port, prefer_grpc=True)
    ensure_collection(client, args.collection, dim=325, recreate=args.recreate)

    upsert_batches(client, args.collection, vec, batch_size=args.batch_size)

    # Validate count
    try:
        c = client.count(args.collection, exact=True).count
        logger.info(f"Collection '{args.collection}' now contains {c} points")
    except Exception:
        pass


if __name__ == '__main__':
    main()

