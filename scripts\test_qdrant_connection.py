#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick connectivity test for a local Qdrant instance.

This script will:
1) Connect to Qdrant (defaults: localhost:6333 HTTP, 6334 gRPC)
2) List collections
3) Create a temporary test collection (4-D COSINE)
4) Upsert a couple of points
5) Run a similarity search (w/ and w/o payload filter)
6) Clean up the test collection

Usage:
    python scripts/test_qdrant_connection.py \
        --host localhost --http-port 6333 --grpc-port 6334 \
        --collection connectivity_test

Dependencies:
    pip install -U qdrant-client numpy
"""

import argparse
import sys
import time
from typing import List

import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.http import models as qmodels


def main():
    ap = argparse.ArgumentParser(description="Test Qdrant connectivity and basic CRUD")
    ap.add_argument('--host', default='localhost')
    ap.add_argument('--http-port', type=int, default=6333)
    ap.add_argument('--grpc-port', type=int, default=6334)
    ap.add_argument('--collection', default='connectivity_test')
    ap.add_argument('--keep', action='store_true', help='Keep the test collection (no cleanup)')
    args = ap.parse_args()

    print(f"[Qdrant Test] Connecting to {args.host}:{args.http_port} (gRPC {args.grpc_port}) ...")
    client = QdrantClient(host=args.host, port=args.http_port, grpc_port=args.grpc_port, prefer_grpc=True, timeout=10.0)

    # 1) List collections
    try:
        cols = client.get_collections().collections
        names = [c.name for c in cols]
        print(f"[Qdrant Test] get_collections OK. Existing collections: {names}")
    except Exception as e:
        print(f"[Qdrant Test] ERROR: Unable to list collections -> {e}")
        sys.exit(1)

    cname = args.collection

    # 2) Create test collection (if not exists)
    try:
        existing = [c.name for c in client.get_collections().collections]
        if cname not in existing:
            client.create_collection(
                collection_name=cname,
                vectors_config=qmodels.VectorParams(size=4, distance=qmodels.Distance.COSINE),
                hnsw_config=qmodels.HnswConfigDiff(m=16, ef_construct=200),
            )
            # Add simple payload indexes
            for field, ftype in [
                ('group', qmodels.PayloadSchemaType.KEYWORD),
                ('value', qmodels.PayloadSchemaType.INTEGER),
            ]:
                try:
                    client.create_payload_index(cname, field_name=field, field_schema=ftype)
                except Exception:
                    pass
            print(f"[Qdrant Test] Created collection '{cname}'")
        else:
            print(f"[Qdrant Test] Using existing collection '{cname}'")
    except Exception as e:
        print(f"[Qdrant Test] ERROR: Create collection failed -> {e}")
        sys.exit(2)

    # 3) Upsert a couple of points
    try:
        pts: List[qmodels.PointStruct] = [
            qmodels.PointStruct(id=1, vector=[0.1, 0.2, 0.3, 0.4], payload={'group': 'A', 'value': 1}),
            qmodels.PointStruct(id=2, vector=[0.2, 0.1, 0.0, 0.9], payload={'group': 'B', 'value': 2}),
        ]
        client.upsert(collection_name=cname, points=pts, wait=True)
        print(f"[Qdrant Test] Upsert OK: {len(pts)} points")
    except Exception as e:
        print(f"[Qdrant Test] ERROR: Upsert failed -> {e}")
        sys.exit(3)

    # 4) Search without filter
    try:
        query = [0.1, 0.2, 0.3, 0.4]
        res = client.search(collection_name=cname, query_vector=query, limit=2, with_payload=True)
        print("[Qdrant Test] Search (no filter) results:")
        for r in res:
            print(f"  id={r.id}, score={r.score:.4f}, payload={r.payload}")
    except Exception as e:
        print(f"[Qdrant Test] ERROR: Search failed -> {e}")
        sys.exit(4)

    # 5) Search with filter (group == 'A')
    try:
        filt = qmodels.Filter(must=[qmodels.FieldCondition(key='group', match=qmodels.MatchValue(value='A'))])
        res = client.search(collection_name=cname, query_vector=[0.1, 0.2, 0.3, 0.4], limit=2, with_payload=True, query_filter=filt)
        print("[Qdrant Test] Search (filter group=='A') results:")
        for r in res:
            print(f"  id={r.id}, score={r.score:.4f}, payload={r.payload}")
    except Exception as e:
        print(f"[Qdrant Test] ERROR: Filtered search failed -> {e}")
        sys.exit(5)

    # 6) Count points
    try:
        cnt = client.count(cname, exact=True).count
        print(f"[Qdrant Test] Count OK: {cnt} points in '{cname}'")
    except Exception as e:
        print(f"[Qdrant Test] WARNING: Count failed -> {e}")

    # 7) Cleanup
    if not args.keep:
        try:
            client.delete_collection(cname)
            print(f"[Qdrant Test] Deleted test collection '{cname}' (use --keep to retain)")
        except Exception as e:
            print(f"[Qdrant Test] WARNING: Cleanup failed -> {e}")

    print("[Qdrant Test] All done. Connectivity OK.")


if __name__ == '__main__':
    main()

