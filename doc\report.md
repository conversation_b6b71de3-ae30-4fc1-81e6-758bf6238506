# 2021年前瞻性预测报告（无标签演示版）

## 任务与数据
- 目标：使用2010–2020年财务向量训练，前瞻性预测2021年个股上涨概率与方向
- 数据：Qdrant向量库 financial_vectors，条目数：44,408（2010–2020）
- 向量：325维降维后财务特征（含工程化与同比）+ 元信息 industry/market/exchange/size_bucket
- 限制：缺乏2021年真实收益标签，当前属于无标签预测演示

## 方法与实现
- 数据加载：FinancialDataLoader（Qdrant滚动读取指定年份向量与payload）
- 特征工程：FeatureEngineer（向量标准化 + 行业/市场/交易所/市值分箱 OneHot）
- 标签生成（训练阶段）：
  - 趋势型伪标签（labels.generate_trend_labels）：基于行业质心在相邻年份的运动方向
  - 备选：无监督聚类 + 行业相对表现
- 数学模型：
  - BayesianKDEClassifier：以KDE估计似然 p(x|Up/Down)，Bayes后验 P(Up|x)
  - MVNGaussian：多元正态密度估计，用于异常度/置信度诊断
  - fundamental_var：用线性模型残差分位数近似基本面VaR
  - 组合指标：Sharpe/Information Ratio

## 预测输出（示意）
- 字段：ts_code, name, industry, size_bucket, prob_up, pred, confidence, risk_grade
- 维度分析：行业、size_bucket 汇总上涨概率
- 投资建议：
  - 选择 prob_up Top 50 构建等权组合
  - 行业权重可按行业胜率加权
  - 风险控制：剔除 MVN 密度最低分位（异常点）与 VaR 显著偏低者

## 理论说明（摘要）
- 贝叶斯分类：P(涨|x) = p(x|涨)P(涨) / (p(x|涨)P(涨) + p(x|跌)P(跌))
- MVN：x ~ N(μ, Σ)，pdf(x) = (2π)^(-d/2) |Σ|^-1/2 exp(-1/2 (x-μ)^T Σ^-1 (x-μ))
- VaR：r = f(X) + ε，VaR_α ~ Quantile(ε, α)
- 夏普比率：S = (E[r] - r_f)/σ(r)；信息比率：IR = E[r-p] / σ(r-p)

## 局限与建议
- 无2021真实价格 → 无法回测准确率；建议补充价格数据进行监督训练与评估
- 进一步提升：
  - 增加技术因子与宏观因子
  - 使用XGBoost/LightGBM并做超参优化
  - 引入时序模型（LSTM/Temporal-Fusion）


