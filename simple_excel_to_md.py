#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Excel转Markdown脚本
"""

import os
import pandas as pd
from pathlib import Path


def convert_excel_to_markdown():
    """将docs目录下的所有Excel文件转换为Markdown"""
    
    # 设置目录
    docs_dir = Path("docs")
    output_dir = Path("docs_markdown")
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    # 查找Excel文件
    excel_files = list(docs_dir.glob("*.xlsx")) + list(docs_dir.glob("*.xls"))
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    for excel_file in excel_files:
        print(f"正在处理: {excel_file.name}")
        
        try:
            # 读取Excel文件
            excel_data = pd.ExcelFile(excel_file)
            
            # 创建Markdown文件
            md_file = output_dir / f"{excel_file.stem}.md"
            
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(f"# {excel_file.stem}\n\n")
                
                # 处理每个工作表
                for sheet_name in excel_data.sheet_names:
                    print(f"  处理工作表: {sheet_name}")
                    
                    # 读取工作表数据
                    df = pd.read_excel(excel_file, sheet_name=sheet_name)
                    
                    # 如果有多个工作表，添加工作表标题
                    if len(excel_data.sheet_names) > 1:
                        f.write(f"## {sheet_name}\n\n")
                    
                    # 转换为Markdown表格
                    if not df.empty:
                        # 处理列名
                        df.columns = [str(col) if pd.notna(col) else f"列{i+1}" 
                                    for i, col in enumerate(df.columns)]
                        
                        # 限制显示行数（避免文件过大）
                        if len(df) > 50:
                            df_display = df.head(50)
                            f.write(f"*显示前50行，原表格共{len(df)}行*\n\n")
                        else:
                            df_display = df
                        
                        # 转换为Markdown
                        md_table = df_display.to_markdown(index=False, tablefmt="pipe")
                        f.write(md_table)
                        f.write("\n\n")
                    else:
                        f.write("*此工作表为空*\n\n")
            
            print(f"  转换完成: {md_file}")
            
        except Exception as e:
            print(f"  处理文件 {excel_file.name} 时出错: {str(e)}")
    
    print(f"\n转换完成！Markdown文件保存在 {output_dir} 目录下")


if __name__ == "__main__":
    convert_excel_to_markdown()
