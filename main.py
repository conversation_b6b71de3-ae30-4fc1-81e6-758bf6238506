# -*- coding: utf-8 -*-
"""
Main entry: load vectors from Qdrant, engineer features, train Bayesian classifier,
MVN density and VaR utilities, and produce 2021 forward-looking predictions.

注意：当前无2021真实价格标签，训练标签采用“行业趋势投影”伪标签；
预测基于2020年的特征向量输出2021年的前瞻性方向与概率。
"""
import argparse
import os
import time
import numpy as np
import pandas as pd

from src.data_loader import FinancialDataLoader
from src.feature_engineer import FeatureEngineer
from src.labels import generate_trend_labels_with_scores
from src.math_models import BayesianKDEClassifier, MVNGaussian, fundamental_var, sharpe_ratio, information_ratio


def main():
    ap = argparse.ArgumentParser(description='基于Qdrant向量的2021年前瞻性涨跌预测管线（无标签）')
    ap.add_argument('--host', default='localhost')
    ap.add_argument('--http-port', type=int, default=6333)
    ap.add_argument('--grpc-port', type=int, default=6334)
    ap.add_argument('--collection', default='financial_vectors')
    ap.add_argument('--train-start', type=int, default=2010)
    ap.add_argument('--train-end', type=int, default=2020, help='用于训练的最后一年（用于构造(t,t+1)对的t+1<=train_end）')
    ap.add_argument('--predict-year', type=int, default=2021, help='预测年份（使用上一年特征向量进行预测）')
    ap.add_argument('--output', default='predictions_2021.csv')
    args = ap.parse_args()

    print(f"[信息] 正在连接Qdrant: {args.host}:{args.http_port} (gRPC {args.grpc_port}) ...")
    loader = FinancialDataLoader(collection=args.collection, host=args.host,
                                 http_port=args.http_port, grpc_port=args.grpc_port)

    # 1) 构建年度数据映射：year -> (X, meta)
    years = list(range(args.train_start, args.train_end + 1))
    year_to_X_meta = {}
    print(f"[信息] 载入年度向量：{years} ... 这可能需要数十秒")
    for y in years:
        Xy, My = loader.load_vectors_for_year(y)
        if not Xy.empty:
            year_to_X_meta[y] = (Xy, My)
    if not year_to_X_meta:
        print('[错误] 未从Qdrant载入任何年度数据，请检查集合与索引。')
        return

    # 2) 伪标签生成（行业质心运动趋势）
    print('[信息] 基于行业趋势生成训练标签（伪标签）...')
    X_train_raw, y_train, M_train, r_train = generate_trend_labels_with_scores(year_to_X_meta, years)
    if X_train_raw.empty or len(y_train) == 0:
        print('[错误] 无法生成训练数据/标签，请检查年度覆盖与行业字段。')
        return
    print(f"[信息] 训练样本数：{len(y_train)}，正类占比：{y_train.mean():.3f}")

    # 3) 特征工程：标准化325维 + OHE(meta)
    print('[信息] 正在进行特征工程（标准化与OneHot）...')
    fe = FeatureEngineer()
    Z_train = fe.fit_transform(X_train_raw, M_train)

    # 4) 数学模型训练
    print('[信息] 训练贝叶斯KDE分类器...')
    bayes = BayesianKDEClassifier()
    # 提醒：KDE在高维度上会较慢，Z_train已包含降维与标准化 + OHE，仍然可能较宽。可在生产中做进一步特征选择。
    bayes.fit(Z_train, y_train.values)

    print('[信息] 训练多元正态模型用于密度/置信度诊断...')
    mvn = MVNGaussian().fit(Z_train)

    print('[信息] 基于训练集投影得分r，拟合基本面VaR（线性残差分位）...')
    # 这里使用 r_train（行业方向投影值）作为收益代理，仅用于风控演示
    var_alpha = 0.05
    var_value, lin_model = fundamental_var(Z_train, r_train, alpha=var_alpha)

    # 5) 预测集：使用2020年的特征向量，推出2021年的前瞻性预测
    predict_feature_year = args.predict_year - 1
    print(f"[信息] 载入预测所需的上一年特征：{predict_feature_year}")
    X_pred_raw, M_pred = loader.load_vectors_for_year(predict_feature_year)
    if X_pred_raw.empty:
        print('[错误] 预测所需的上一年特征向量不存在，请检查Qdrant是否包含该年数据。')
        return
    Z_pred = fe.transform(X_pred_raw, M_pred)

    print('[信息] 计算后验上涨概率 P(涨|x) 与密度置信指标...')
    proba = bayes.predict_proba(Z_pred)[:, 1]
    density = mvn.pdf(Z_pred)
    # 置信度：对密度做分位归一化（相对训练分布）
    train_density = mvn.pdf(Z_train)
    thresh = np.quantile(train_density, 0.1)
    confidence = np.clip((density - thresh) / (np.max(train_density) - thresh + 1e-12), 0, 1)

    # 风险评级（示意）：VaR基线 + 密度低则风险高
    risk_grade = np.where(density < np.quantile(train_density, 0.1), '高',
                   np.where(density < np.quantile(train_density, 0.3), '中', '低'))

    pred_dir = (proba > 0.5).astype(int)

    # 6) 汇总结果并导出
    res = M_pred.copy()
    res['prob_up'] = proba
    res['pred'] = pred_dir
    res['confidence'] = confidence
    res['risk_grade'] = risk_grade

    # 维度分析
    by_industry = res.groupby('industry')['pred'].mean().sort_values(ascending=False)
    by_size = res.groupby('size_bucket')['pred'].mean().sort_values(ascending=False)
    by_exchange = res.groupby('exchange')['pred'].mean().sort_values(ascending=False)

    # 组合建议（示意）：选择置信度>0.5且上涨概率Top 200构建等权组合
    res_sorted = res.sort_values(['prob_up','confidence'], ascending=False)
    portfolio = res_sorted[(res_sorted['confidence'] > 0.5)].head(200)

    # 组合风险度量参考（基于训练r_train）：输出训练集上的Sharpe/IR（演示）
    # 注意：无真实2021收益，以下仅做训练参考
    sr = sharpe_ratio(r_train)
    # 构造一个基准（训练r_train的中位数常数序列），用于IR演示
    benchmark = np.median(r_train) * np.ones_like(r_train)
    ir = information_ratio(r_train, benchmark)

    # 导出CSV
    out_csv = args.output
    res.to_csv(out_csv, index=False)

    # 输出Markdown报告到 /doc/report.md
    os.makedirs('doc', exist_ok=True)
    md_path = 'doc/report.md'
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write('# 2021年前瞻性预测报告（无标签）\n\n')
        f.write('## 数据与目标\n')
        f.write(f'- 训练年份：{years}；预测年份：{args.predict_year}（使用{predict_feature_year}年特征）\n')
        f.write(f'- 训练样本数：{len(y_train)}；预测样本数：{len(res)}\n\n')

        f.write('## 数学模型与原理\n')
        f.write('- 贝叶斯分类（KDE似然）：P(涨|x) = p(x|涨)P(涨) / (p(x|涨)P(涨) + p(x|跌)P(跌))\n')
        f.write('- 多元正态（MVN）密度：用于置信度/异常度诊断\n')
        f.write(f'- 基本面VaR（α={var_alpha}）：线性残差分位近似\n')
        f.write('- 组合指标：夏普比率与信息比率（训练期参考）\n\n')

        f.write('## 预测结果概览\n')
        f.write('- 字段：ts_code, name, industry, exchange, size_bucket, prob_up, pred, confidence, risk_grade\n\n')
        f.write('### Top 50（按概率与置信度排序）\n')
        f.write(res_sorted.head(50)[['ts_code','name','industry','exchange','size_bucket','prob_up','confidence','pred','risk_grade']].to_markdown(index=False))
        f.write('\n\n### 行业趋势（平均上涨概率）\n')
        f.write(by_industry.reset_index().rename(columns={'pred':'avg_up_prob'}).to_markdown(index=False))
        f.write('\n\n### 市值趋势（平均上涨概率）\n')
        f.write(by_size.reset_index().rename(columns={'pred':'avg_up_prob'}).to_markdown(index=False))
        f.write('\n\n### 交易所/地区趋势（平均上涨概率）\n')
        f.write(by_exchange.reset_index().rename(columns={'pred':'avg_up_prob'}).to_markdown(index=False))

        f.write('\n\n## 投资组合建议与风险控制（示意）\n')
        f.write('- 组合构建：选择prob_up与confidence靠前的200只等权；行业权重可按行业胜率加权\n')
        f.write('- 风险控制：剔除MVN密度低于10%分位的样本；关注VaR分位低的组合敞口\n')
        f.write(f'- 训练期参考：Sharpe≈{sr:.3f}；Information Ratio≈{ir:.3f}\n\n')

        f.write('## 可信度与局限\n')
        f.write('- 当前缺乏2021真实收益，预测无法回测；待补充价格数据后应进行全面验证\n')
        f.write('- KDE在高维可能慢且过拟合，生产建议先做特征选择/降维与调参\n')
        f.write('- MVN假设为高斯近似，主要用于相对异常度参考\n\n')

        f.write('## 验证与回测方法论（待有标签时）\n')
        f.write('- 使用2021真实收益方向作为y，计算准确率、AUC、F1；进行滚动窗口与时间切片验证\n')
        f.write('- 组合回测：比较预测组合与基准指数的超额、夏普、最大回撤\n')

    print(f"[信息] 已生成预测CSV: {out_csv} 与报告: {md_path}")


if __name__ == '__main__':
    main()

