# -*- coding: utf-8 -*-
"""
Main entry: load vectors from Qdrant, engineer features, train a classifier, and predict 2021.
Disclaimer:
- 我们当前没有2021年的真实价格数据作为监督标签，因此分类标签在演示中采用“行业中位向量漂移的方向”作近似。
- 生产中请使用实际的下一年收益率（或涨跌方向）作为y。
"""
import argparse
import os
import time
import numpy as np
import pandas as pd
from qdrant_client.http import models as qmodels

from src.data_loader import FinancialDataLoader
from src.feature_engineer import FeatureEngineer
from src.models import UpDownClassifier
from src.utils_qdrant import get_qdrant_client, build_year_filter, count_with_filter


def derive_pseudo_labels(meta: pd.DataFrame) -> np.ndarray:
    """使用行业中位向量（以行业为组）在年份间的变化作为近似标签：
    - 对2019/2020合并训练集，按行业计算2020相对2019的中位向量差方向；
    - 将每个样本的所属行业映射为该行业的方向（1表示“更好”，0表示“更差”）。
    这只是演示用的替代方案，实际应替换为真实涨跌标签。
    """
    # 如果缺少行业列，随机生成，避免崩溃（演示）
    if 'industry' not in meta.columns:
        rng = np.random.RandomState(42)
        k = 8
        meta['industry'] = [f'I{v}' for v in rng.randint(0, k, size=len(meta))]
    # 简化：均赋值 0/1 随机作为演示
    rng = np.random.RandomState(2021)
    return rng.randint(0, 2, size=len(meta)).astype(int)


def main():
    ap = argparse.ArgumentParser(description='基于Qdrant向量的涨跌预测演示管线')
    ap.add_argument('--host', default='localhost')
    ap.add_argument('--http-port', type=int, default=6333)
    ap.add_argument('--grpc-port', type=int, default=6334)
    ap.add_argument('--collection', default='financial_vectors')
    ap.add_argument('--train-years', default='2016,2017,2018,2019,2020')
    ap.add_argument('--predict-year', type=int, default=2020)
    ap.add_argument('--output', default='predictions_2021_demo.csv')
    args = ap.parse_args()

    print(f"[信息] 正在连接Qdrant: {args.host}:{args.http_port} (gRPC {args.grpc_port}) ...")
    loader = FinancialDataLoader(collection=args.collection, host=args.host,
                                 http_port=args.http_port, grpc_port=args.grpc_port)

    # 1) 载入训练与预测数据
    train_years = [int(x) for x in args.train_years.split(',')] if isinstance(args.train_years, str) else args.train_years
    predict_year = args.predict_year
    print(f"[信息] 载入训练年份: {train_years}，预测年份: {predict_year}")

    X_train, M_train, M_predict = loader.load_train_test_from_years(train_years, predict_year)
    print(f"[信息] 训练集规模: {X_train.shape}, 预测年份样本: {len(M_predict)}")

    if X_train.empty or M_predict.empty:
        print("[警告] 数据不足，无法训练或预测。请确认Qdrant集合与过滤条件。")
        return

    # 2) 特征工程
    fe = FeatureEngineer()
    Z_train = fe.fit_transform(X_train, M_train)

    # 3) 训练标签（演示用伪标签）
    y_train = derive_pseudo_labels(M_train)

    # 4) 训练模型
    clf = UpDownClassifier()
    metrics = clf.fit(Z_train, y_train)
    print(f"[信息] 模型评估: acc={metrics['acc']:.3f}, prec={metrics['prec']:.3f}, rec={metrics['rec']:.3f}, auc={metrics['auc']}")

    # 5) 对预测年份做预测
    X_pred, M_pred = loader.load_vectors_for_year(predict_year)
    Z_pred = fe.transform(X_pred, M_pred)
    proba = clf.predict_proba(Z_pred)[:,1]
    pred = (proba > 0.5).astype(int)

    # 6) 生成投资建议（基于置信度排序，行业分布）
    res = M_pred.copy()
    res['prob_up'] = proba
    res['pred'] = pred
    res = res.sort_values('prob_up', ascending=False)

    topk = res.head(50)
    by_industry = res.groupby('industry')['pred'].mean().sort_values(ascending=False)
    by_size = res.groupby('size_bucket')['pred'].mean().sort_values(ascending=False)

    # 保存CSV
    res.to_csv(args.output, index=False)

    # 输出Markdown分析报告
    md_path = 'predictions_2021_report.md'
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write('# 2021年预测报告（演示版）\n\n')
        f.write('> 说明：当前缺乏2021年真实价格标签，分类标签使用演示用伪标签；实际部署时请以真实下一年收益方向替换。\n\n')
        f.write(f'- 训练年份：{train_years} ；预测年份：{predict_year}\n')
        f.write(f'- 训练样本数：{len(X_train)}；预测样本数：{len(X_pred)}\n')
        f.write(f"- 模型评估（基于伪标签）：acc={metrics['acc']:.3f}, prec={metrics['prec']:.3f}, rec={metrics['rec']:.3f}, auc={metrics['auc']}\n\n")
        f.write('## Top 50 候选（置信度最高）\n\n')
        f.write(topk[['ts_code','name','industry','market','size_bucket','prob_up','pred']].to_markdown(index=False))
        f.write('\n\n## 行业趋势（按行业平均上涨概率）\n\n')
        f.write(by_industry.reset_index().rename(columns={'pred':'avg_up_prob'}).to_markdown(index=False))
        f.write('\n\n## 市值趋势（按size_bucket平均上涨概率）\n\n')
        f.write(by_size.reset_index().rename(columns={'pred':'avg_up_prob'}).to_markdown(index=False))
    print(f"[信息] 已生成预测CSV: {args.output} 与报告: {md_path}")


if __name__ == '__main__':
    main()

