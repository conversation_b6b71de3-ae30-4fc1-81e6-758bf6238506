# -*- coding: utf-8 -*-
"""
Feature engineering on 325-D vectors + payload metadata.
We will augment vectors with simple meta-derived features (industry one-hot, size_bucket),
and standardize features.
"""
from typing import Tuple, List
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline


class FeatureEngineer:
    def __init__(self):
        self.pipeline: Pipeline = None
        self.feature_names_: List[str] = []

    def build_pipeline(self, X: pd.DataFrame, meta: pd.DataFrame) -> Pipeline:
        # Base numeric are the 325 dims
        numeric_features = list(X.columns)
        # Categorical from payload
        cat_features = []
        for c in ['industry', 'market', 'exchange', 'size_bucket']:
            if c in meta.columns:
                cat_features.append(c)
            else:
                meta[c] = ''
                cat_features.append(c)
        pre = ColumnTransformer(
            transformers=[
                ('num', StandardScaler(), numeric_features),
                ('cat', OneHotEncoder(handle_unknown='ignore', sparse_output=False), cat_features),
            ]
        )
        self.pipeline = Pipeline([
            ('pre', pre),
        ])
        return self.pipeline

    def fit_transform(self, X: pd.DataFrame, meta: pd.DataFrame) -> np.ndarray:
        if self.pipeline is None:
            self.build_pipeline(X, meta)

        # 确保所有列名都是字符串
        X_copy = X.copy()
        X_copy.columns = X_copy.columns.astype(str)

        # 准备分类特征
        meta_copy = meta.copy()
        cols = ['industry','market','exchange','size_bucket']
        for c in cols:
            if c not in meta_copy.columns:
                meta_copy[c] = ''

        # 合并数据并确保列名一致
        combined = pd.concat([X_copy, meta_copy[cols]], axis=1)
        combined.columns = combined.columns.astype(str)

        Z = self.pipeline.fit_transform(combined)

        # Build feature names
        num_names = [f'v{i}' for i in range(X.shape[1])]
        cat_names = []
        ohe = self.pipeline.named_steps['pre'].named_transformers_['cat']
        cat_names = list(ohe.get_feature_names_out(['industry','market','exchange','size_bucket']))
        self.feature_names_ = num_names + cat_names
        return Z

    def transform(self, X: pd.DataFrame, meta: pd.DataFrame) -> np.ndarray:
        # 确保所有列名都是字符串
        X_copy = X.copy()
        X_copy.columns = X_copy.columns.astype(str)

        # 准备分类特征
        meta_copy = meta.copy()
        cols = ['industry','market','exchange','size_bucket']
        for c in cols:
            if c not in meta_copy.columns:
                meta_copy[c] = ''

        # 合并数据并确保列名一致
        combined = pd.concat([X_copy, meta_copy[cols]], axis=1)
        combined.columns = combined.columns.astype(str)

        return self.pipeline.transform(combined)

