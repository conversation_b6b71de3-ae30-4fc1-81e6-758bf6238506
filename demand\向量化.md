# 股票向量化数据的数学分析与概率模型

针对股票价格向量化后的数据，确实可以应用多种数学分析和概率模型来计算期望值、分布概率等统计量。以下是几种主要方法：

## 1. 收益率分布的数学期望与方差

对于对数收益率向量 **r** = [r₁, r₂, ..., rₙ]，其中 rᵢ = ln(pᵢ/pᵢ₋₁):

- **样本期望(平均收益率)**:
  ```math
  \hat{\mu} = \frac{1}{n}\sum_{i=1}^n r_i
  ```

- **样本方差(波动率)**:
  ```math
  \hat{\sigma}^2 = \frac{1}{n-1}\sum_{i=1}^n (r_i - \hat{\mu})^2
  ```

## 2. 正态分布假设下的概率计算

假设收益率服从正态分布 r ~ N(μ, σ²):

- **某收益率区间的概率**:
  ```math
  P(a < r < b) = \Phi\left(\frac{b-\mu}{\sigma}\right) - \Phi\left(\frac{a-\mu}{\sigma}\right)
  ```
  其中Φ为标准正态CDF

- **VaR(风险价值)计算**:
  ```math
  \text{VaR}_\alpha = \mu + \sigma\Phi^{-1}(\alpha)
  ```

## 3. 二项分布模型的应用

将价格变动简化为上涨/下跌两种状态:

- **n天内k次上涨的概率**:
  ```math
  P(k;n,p) = C_n^k p^k (1-p)^{n-k}
  ```
  其中p可根据历史数据估计为上涨概率

## 4. 布朗运动模型

股票价格可建模为几何布朗运动:
```math
dS_t = \mu S_t dt + \sigma S_t dW_t
```
其解为:
```math
S_t = S_0 \exp\left((\mu-\frac{1}{2}\sigma^2)t + \sigma W_t\right)
```

## 5. 极值理论(EVT)

用于估计极端价格变动的概率:
```math
P(X > u) ≈ [1 + ξ(x-u)/β]^{-1/ξ}
```

## Python实现示例

```python
import numpy as np
from scipy.stats import norm, binom

# 假设有以下收益率数据
returns = np.array([0.01, -0.02, 0.015, -0.01, 0.03])

# 计算期望和波动率
mu = np.mean(returns)
sigma = np.std(returns)

# 计算收益率在-0.01到0.01之间的概率
prob = norm.cdf(0.01, mu, sigma) - norm.cdf(-0.01, mu, sigma)

# 二项分布示例(假设20天内有12天上涨，上涨概率0.55)
p = 0.55
binom_prob = binom.pmf(12, 20, p)

# 计算95% VaR
var_95 = norm.ppf(0.05, mu, sigma)
```

## 实际应用中的注意事项

1. **厚尾现象**：实际收益率分布常比正态分布有更厚的尾部
2. **波动聚集**：波动率不是恒定的，可采用GARCH模型
3. **非对称性**：下跌和上涨的分布可能不对称
4. **时变参数**：市场状况变化时参数也会变化

这些数学模型为量化分析提供了基础框架，但实际应用中需要结合市场特性和更复杂的模型进行调整。