#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的2021年股票涨跌预测生成脚本
修复标签不平衡和模型过拟合问题

主要改进:
1. 更平衡的标签生成策略
2. 多种模型集成 (随机森林 + 逻辑回归 + XGBoost)
3. 更稳健的特征选择和验证
4. 详细的模型诊断和调试信息
"""

import argparse
import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
from tqdm import tqdm
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_classif

try:
    from src.data_loader import FinancialDataLoader
    from src.utils_qdrant import get_qdrant_client, count_with_filter
except ImportError as e:
    print(f"[错误] 无法导入必要模块: {e}")
    sys.exit(1)


def generate_balanced_labels(year_to_X_meta: Dict[int, Tuple[pd.DataFrame, pd.DataFrame]], 
                           years: List[int]) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame]:
    """生成更平衡的标签"""
    print("[信息] 使用改进的标签生成策略...")
    
    X_list, y_list, M_list = [], [], []
    
    for i in range(len(years) - 1):
        t, t1 = years[i], years[i + 1]
        if t not in year_to_X_meta or t1 not in year_to_X_meta:
            continue
            
        X_t, M_t = year_to_X_meta[t]
        X_t1, M_t1 = year_to_X_meta[t1]
        
        if X_t.empty or X_t1.empty or 'ts_code' not in M_t.columns or 'ts_code' not in M_t1.columns:
            continue
        
        # 构建公司映射
        map_t = M_t.set_index('ts_code')
        map_t1 = M_t1.set_index('ts_code')
        common_codes = sorted(set(map_t.index).intersection(set(map_t1.index)))
        
        if len(common_codes) < 100:
            continue
        
        print(f"  处理 {t}->{t1}: {len(common_codes)} 家公司")
        
        # 策略1: 基于财务指标改善程度
        improvements = []
        for code in common_codes:
            try:
                idx_t = M_t.index[M_t['ts_code'] == code][0]
                idx_t1 = M_t1.index[M_t1['ts_code'] == code][0]
                
                x_t = X_t.iloc[idx_t].values
                x_t1 = X_t1.iloc[idx_t1].values
                
                # 计算向量变化的L2范数
                change_magnitude = np.linalg.norm(x_t1 - x_t)
                
                # 计算关键财务指标的改善 (假设前几维是重要指标)
                key_features = x_t1[:20] - x_t[:20]  # 前20个特征
                improvement_score = np.mean(key_features)
                
                improvements.append((code, idx_t, improvement_score, change_magnitude))
                
            except Exception:
                continue
        
        if len(improvements) < 50:
            continue
        
        # 按改善分数排序，取上下各30%作为正负样本
        improvements.sort(key=lambda x: x[2])
        n_samples = len(improvements)
        
        # 下30%标记为0（恶化），上30%标记为1（改善）
        bottom_30pct = int(n_samples * 0.3)
        top_30pct = int(n_samples * 0.7)
        
        selected_samples = improvements[:bottom_30pct] + improvements[top_30pct:]
        
        for code, idx_t, score, magnitude in selected_samples:
            label = 0 if score < improvements[top_30pct][2] else 1
            X_list.append(X_t.iloc[idx_t:idx_t+1])
            M_list.append(M_t.iloc[idx_t:idx_t+1])
            y_list.append(label)
    
    if not X_list:
        return pd.DataFrame(), pd.Series(dtype=int), pd.DataFrame()
    
    X_train = pd.concat(X_list, ignore_index=True)
    y_train = pd.Series(y_list, name='label')
    M_train = pd.concat(M_list, ignore_index=True)
    
    return X_train, y_train, M_train


class ImprovedStockPredictor:
    """改进的股票预测器"""
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.feature_selector = SelectKBest(f_classif, k=100)
        self.feature_names = []
        
    def train_ensemble(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """训练集成模型"""
        print("[信息] 训练集成模型...")
        
        # 特征选择
        print("  - 选择最重要的100个特征")
        X_selected = self.feature_selector.fit_transform(X, y)
        
        # 标准化
        X_scaled = self.scaler.fit_transform(X_selected)
        
        # 训练多个模型
        self.models['rf'] = RandomForestClassifier(
            n_estimators=300, 
            max_depth=12, 
            random_state=42,
            class_weight='balanced'
        )
        
        self.models['lr'] = LogisticRegression(
            random_state=42, 
            class_weight='balanced',
            max_iter=1000
        )
        
        # 交叉验证评估
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        metrics = {}
        
        for name, model in self.models.items():
            print(f"  - 训练 {name} 模型")
            model.fit(X_scaled, y)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
            metrics[f'{name}_cv_auc'] = float(cv_scores.mean())
            metrics[f'{name}_cv_std'] = float(cv_scores.std())
            
            print(f"    {name} CV AUC: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        
        # 预测并生成分类报告
        y_pred_rf = self.models['rf'].predict(X_scaled)
        y_pred_lr = self.models['lr'].predict(X_scaled)
        
        print("\n随机森林分类报告:")
        print(classification_report(y, y_pred_rf, target_names=['下跌', '上涨']))
        
        print("\n逻辑回归分类报告:")
        print(classification_report(y, y_pred_lr, target_names=['下跌', '上涨']))
        
        return metrics
    
    def predict_ensemble(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """集成预测"""
        # 特征选择和标准化
        X_selected = self.feature_selector.transform(X)
        X_scaled = self.scaler.transform(X_selected)
        
        # 获取各模型预测
        proba_rf = self.models['rf'].predict_proba(X_scaled)[:, 1]
        proba_lr = self.models['lr'].predict_proba(X_scaled)[:, 1]
        
        # 集成预测 (简单平均)
        proba_ensemble = (proba_rf + proba_lr) / 2
        pred_ensemble = (proba_ensemble > 0.5).astype(int)
        
        return proba_ensemble, pred_ensemble


def main():
    parser = argparse.ArgumentParser(description='改进的2021年股票涨跌预测')
    parser.add_argument('--host', default='localhost')
    parser.add_argument('--http-port', type=int, default=6333)
    parser.add_argument('--grpc-port', type=int, default=6334)
    parser.add_argument('--collection', default='financial_vectors')
    parser.add_argument('--train-start', type=int, default=2015)
    parser.add_argument('--train-end', type=int, default=2020)
    parser.add_argument('--predict-year', type=int, default=2021)
    parser.add_argument('--output', default='predictions_2021_improved.csv')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🔧 改进版2021年股票涨跌预测系统")
    print("=" * 80)
    
    # 初始化数据加载器
    loader = FinancialDataLoader(
        collection=args.collection,
        host=args.host,
        http_port=args.http_port,
        grpc_port=args.grpc_port
    )
    
    # 加载年度数据
    years = list(range(args.train_start, args.train_end + 1))
    year_to_X_meta = {}
    
    print(f"[信息] 加载训练数据 ({args.train_start}-{args.train_end})...")
    for year in tqdm(years, desc="加载年度数据"):
        X_year, M_year = loader.load_vectors_for_year(year)
        if not X_year.empty:
            year_to_X_meta[year] = (X_year, M_year)
            print(f"  {year}年: {len(X_year):,} 条记录")
    
    # 生成平衡标签
    X_train, y_train, M_train = generate_balanced_labels(year_to_X_meta, years)
    
    if X_train.empty:
        print("[错误] 无法生成训练数据")
        return
    
    print(f"\n[信息] 标签分布:")
    print(f"  - 总样本: {len(y_train):,}")
    print(f"  - 上涨样本: {y_train.sum():,} ({y_train.mean():.1%})")
    print(f"  - 下跌样本: {(~y_train.astype(bool)).sum():,} ({1-y_train.mean():.1%})")
    
    # 检查标签平衡性
    if y_train.mean() > 0.8 or y_train.mean() < 0.2:
        print("[警告] 标签仍然不平衡，可能影响模型性能")
    
    # 训练改进的预测器
    predictor = ImprovedStockPredictor()
    metrics = predictor.train_ensemble(X_train.values, y_train.values)
    
    # 生成2021年预测
    feature_year = args.predict_year - 1
    print(f"\n[信息] 使用{feature_year}年特征预测{args.predict_year}年...")
    
    X_pred, M_pred = loader.load_vectors_for_year(feature_year)
    if X_pred.empty:
        print(f"[错误] 无法加载{feature_year}年数据")
        return
    
    proba, pred = predictor.predict_ensemble(X_pred.values)
    
    # 组装结果
    results = M_pred.copy()
    results['prob_up'] = proba
    results['pred_direction'] = pred
    results['confidence'] = np.abs(proba - 0.5) * 2  # 距离0.5越远置信度越高
    
    # 风险评级
    results['risk_grade'] = pd.cut(
        results['confidence'], 
        bins=[0, 0.3, 0.7, 1.0], 
        labels=['高风险', '中风险', '低风险']
    )
    
    # 保存结果
    results.to_csv(args.output, index=False, encoding='utf-8-sig')
    
    # 生成改进的分析报告
    os.makedirs('doc', exist_ok=True)
    with open('doc/report_improved.md', 'w', encoding='utf-8') as f:
        f.write('# 2021年股票涨跌预测报告（改进版）\n\n')
        f.write('## 模型改进说明\n\n')
        f.write('### 主要改进点\n')
        f.write('1. **平衡标签生成**: 基于财务指标改善程度，选择上下30%分位作为正负样本\n')
        f.write('2. **集成模型**: 随机森林 + 逻辑回归集成预测\n')
        f.write('3. **特征选择**: 选择最重要的100个特征避免过拟合\n')
        f.write('4. **交叉验证**: 5折交叉验证评估模型稳定性\n\n')
        
        f.write('## 预测结果统计\n\n')
        f.write(f'- **预测股票总数**: {len(results):,}\n')
        f.write(f'- **预测上涨**: {pred.sum():,} ({pred.mean():.1%})\n')
        f.write(f'- **预测下跌**: {(~pred.astype(bool)).sum():,} ({1-pred.mean():.1%})\n')
        f.write(f'- **平均上涨概率**: {proba.mean():.3f}\n')
        f.write(f'- **概率标准差**: {proba.std():.3f}\n\n')
        
        # 概率分布分析
        f.write('### 概率分布分析\n')
        prob_bins = pd.cut(proba, bins=[0, 0.3, 0.4, 0.6, 0.7, 1.0], 
                          labels=['强烈看跌', '看跌', '中性', '看涨', '强烈看涨'])
        prob_dist = prob_bins.value_counts().sort_index()
        f.write(prob_dist.to_frame('股票数量').to_markdown())
        f.write('\n\n')
        
        # Top推荐 (概率分布更合理的情况下)
        f.write('### Top 50 投资推荐\n')
        top_50 = results.nlargest(50, 'prob_up')
        display_cols = ['ts_code', 'name', 'industry', 'exchange', 'size_bucket', 
                       'prob_up', 'confidence', 'pred_direction', 'risk_grade']
        available_cols = [col for col in display_cols if col in top_50.columns]
        f.write(top_50[available_cols].to_markdown(index=False, floatfmt='.3f'))
        f.write('\n\n')
        
        # 行业分析
        if 'industry' in results.columns:
            industry_stats = results.groupby('industry').agg({
                'prob_up': ['count', 'mean', 'std'],
                'pred_direction': 'mean'
            }).round(3)
            industry_stats.columns = ['股票数量', '平均概率', '概率标准差', '上涨比例']
            f.write('### 行业分析\n')
            f.write(industry_stats.sort_values('平均概率', ascending=False).head(20).to_markdown())
            f.write('\n\n')
        
        # 模型诊断
        f.write('## 模型诊断信息\n\n')
        f.write('### 交叉验证结果\n')
        for key, value in metrics.items():
            f.write(f'- **{key}**: {value:.3f}\n')
        f.write('\n')
        
        f.write('### 标签生成统计\n')
        f.write(f'- **训练样本**: {len(y_train):,}\n')
        f.write(f'- **正负比例**: {y_train.mean():.1%} : {1-y_train.mean():.1%}\n')
        f.write(f'- **标签方差**: {y_train.var():.3f}\n\n')
        
        f.write('### 预测概率分布\n')
        f.write(f'- **最小概率**: {proba.min():.3f}\n')
        f.write(f'- **最大概率**: {proba.max():.3f}\n')
        f.write(f'- **中位概率**: {np.median(proba):.3f}\n')
        f.write(f'- **概率方差**: {proba.var():.3f}\n\n')
        
        if proba.std() < 0.1:
            f.write('⚠️ **警告**: 预测概率方差过小，可能存在模型问题\n\n')
        
        f.write('## 建议与后续步骤\n\n')
        f.write('1. **验证标签质量**: 检查标签生成逻辑是否合理\n')
        f.write('2. **模型调优**: 尝试不同的超参数和特征组合\n')
        f.write('3. **数据增强**: 考虑添加技术指标和宏观因子\n')
        f.write('4. **回测验证**: 使用历史数据进行回测验证\n')
    
    print(f"\n✅ 改进版预测完成!")
    print(f"📁 输出文件: {args.output}")
    print(f"📊 分析报告: doc/report_improved.md")
    print(f"📈 预测概率范围: [{proba.min():.3f}, {proba.max():.3f}]")
    print(f"📊 概率标准差: {proba.std():.3f}")


if __name__ == '__main__':
    main()
