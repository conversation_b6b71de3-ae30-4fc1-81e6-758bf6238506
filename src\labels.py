# -*- coding: utf-8 -*-
"""
Label generation strategies for training without real next-year returns.
Strategy A (trend-based):
- For consecutive years t and t+1, compute industry centroids Ct and Ct1.
- For companies that appear in both t and t+1, compute movement d_i = x_{t+1}-x_t
- Define industry's improvement direction v = Ct1 - Ct (normalize if nonzero)
- Label y_i = 1 if projection <d_i, v> > 0 else 0

This yields (X_t, y) pairs for t in [start, end-1].
"""
from typing import Dict, Tuple, List
import numpy as np
import pandas as pd


def _industry_centroids(X: pd.DataFrame, meta: pd.DataFrame) -> pd.Series:
    df = meta[['industry']].copy()
    if 'industry' not in df.columns:
        df['industry'] = ''
    # per-industry centroid vector (numpy array)
    groups = meta['industry'].fillna('').astype(str)
    centroids = {}
    for ind in groups.unique():
        idx = groups[groups == ind].index
        if len(idx) == 0:
            continue
        centroids[ind] = X.loc[idx].mean(axis=0).values
    return pd.Series(centroids)


def generate_trend_labels(year_to_X_meta: Dict[int, Tuple[pd.DataFrame, pd.DataFrame]],
                           years: List[int]) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame]:
    """
    Returns X_train (concat of year t samples), y (0/1), M_train (aligned metas)
    years: ordered list of years; will pair (t, t+1)
    """
    X_list: List[pd.DataFrame] = []
    y_list: List[np.ndarray] = []
    M_list: List[pd.DataFrame] = []

    for i in range(len(years)-1):
        t = years[i]
        t1 = years[i+1]
        if t not in year_to_X_meta or t1 not in year_to_X_meta:
            continue
        X_t, M_t = year_to_X_meta[t]
        X_t1, M_t1 = year_to_X_meta[t1]
        if X_t.empty or X_t1.empty:
            continue
        # Build ts_code -> row mapping for t and t+1
        if 'ts_code' not in M_t.columns or 'ts_code' not in M_t1.columns:
            continue
        map_t = M_t['ts_code'].astype(str).reset_index().set_index('ts_code')['index']
        map_t1 = M_t1['ts_code'].astype(str).reset_index().set_index('ts_code')['index']
        common = sorted(set(map_t.index).intersection(set(map_t1.index)))
        if not common:
            continue
        # Industry centroids for direction vector
        C_t = _industry_centroids(X_t, M_t)
        C_t1 = _industry_centroids(X_t1, M_t1)
        # Prepare labels for each common ts_code based on its industry at year t
        ys = []
        idx_keep = []
        for code in common:
            i_t = int(map_t.loc[code])
            i_t1 = int(map_t1.loc[code])
            x_t = X_t.loc[i_t].values
            x_t1 = X_t1.loc[i_t1].values
            ind = str(M_t.loc[i_t].get('industry', ''))
            if ind not in C_t.index or ind not in C_t1.index:
                continue
            v = C_t1.loc[ind] - C_t.loc[ind]
            norm = np.linalg.norm(v)
            if norm == 0:
                # No direction change: skip or mark neutral
                continue
            v = v / norm
            d = x_t1 - x_t
            proj = float(np.dot(d, v))
            y = 1 if proj > 0 else 0
            ys.append(y)
            idx_keep.append(i_t)
        if ys:
            y_arr = np.array(ys, dtype=int)
            X_list.append(X_t.loc[idx_keep].reset_index(drop=True))
            M_list.append(M_t.loc[idx_keep].reset_index(drop=True))
            y_list.append(y_arr)
    if not X_list:
        return pd.DataFrame(), pd.Series(dtype=int), pd.DataFrame()
    X_train = pd.concat(X_list, ignore_index=True)
    y = pd.Series(np.concatenate(y_list), name='label')
    M_train = pd.concat(M_list, ignore_index=True)
    return X_train, y, M_train


def generate_trend_labels_with_scores(year_to_X_meta: Dict[int, Tuple[pd.DataFrame, pd.DataFrame]],
                                       years: List[int]) -> Tuple[pd.DataFrame, pd.Series, pd.DataFrame, np.ndarray]:
    """
    Same as generate_trend_labels but also returns a continuous projection score array r (proxy return).
    """
    X_list: List[pd.DataFrame] = []
    y_list: List[np.ndarray] = []
    M_list: List[pd.DataFrame] = []
    r_list: List[np.ndarray] = []

    for i in range(len(years)-1):
        t = years[i]
        t1 = years[i+1]
        if t not in year_to_X_meta or t1 not in year_to_X_meta:
            continue
        X_t, M_t = year_to_X_meta[t]
        X_t1, M_t1 = year_to_X_meta[t1]
        if X_t.empty or X_t1.empty:
            continue
        if 'ts_code' not in M_t.columns or 'ts_code' not in M_t1.columns:
            continue
        map_t = M_t['ts_code'].astype(str).reset_index().set_index('ts_code')['index']
        map_t1 = M_t1['ts_code'].astype(str).reset_index().set_index('ts_code')['index']
        common = sorted(set(map_t.index).intersection(set(map_t1.index)))
        if not common:
            continue
        C_t = _industry_centroids(X_t, M_t)
        C_t1 = _industry_centroids(X_t1, M_t1)
        ys, idx_keep, rs = [], [], []
        for code in common:
            i_t = int(map_t.loc[code])
            i_t1 = int(map_t1.loc[code])
            x_t = X_t.loc[i_t].values
            x_t1 = X_t1.loc[i_t1].values
            ind = str(M_t.loc[i_t].get('industry', ''))
            if ind not in C_t.index or ind not in C_t1.index:
                continue
            v = C_t1.loc[ind] - C_t.loc[ind]
            norm = np.linalg.norm(v)
            if norm == 0:
                continue
            v = v / norm
            d = x_t1 - x_t
            proj = float(np.dot(d, v))
            y = 1 if proj > 0 else 0
            ys.append(y)
            rs.append(proj)
            idx_keep.append(i_t)
        if ys:
            y_arr = np.array(ys, dtype=int)
            r_arr = np.array(rs, dtype=float)
            X_list.append(X_t.loc[idx_keep].reset_index(drop=True))
            M_list.append(M_t.loc[idx_keep].reset_index(drop=True))
            y_list.append(y_arr)
            r_list.append(r_arr)
    if not X_list:
        return pd.DataFrame(), pd.Series(dtype=int), pd.DataFrame(), np.array([])
    X_train = pd.concat(X_list, ignore_index=True)
    y = pd.Series(np.concatenate(y_list), name='label')
    M_train = pd.concat(M_list, ignore_index=True)
    r = np.concatenate(r_list)
    return X_train, y, M_train, r

