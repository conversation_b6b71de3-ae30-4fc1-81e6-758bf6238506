# 基于财务数据的股票涨跌预测分析方案

## 1. 数据概览与特征分析

### 1.1 数据集描述
- **时间跨度**: 2010-2020年（11年历史数据）
- **股票数量**: 4,391只A股上市公司
- **数据维度**: 
  - 利润表：66个财务指标
  - 资产负债表：115个财务指标  
  - 现金流量表：35个财务指标
  - 财务指标表：109个衍生指标
  - 公司基本信息：16个属性字段

### 1.2 核心预测目标
基于历史财务数据预测股票未来涨跌趋势，构建多时间尺度的预测模型：
- **短期预测**（1-30天）：基于最新财务数据和技术指标
- **中期预测**（1-6个月）：基于季度财务报告
- **长期预测**（6-12个月）：基于年度财务趋势

## 2. 特征工程策略

### 2.1 基础财务比率计算

#### 2.1.1 盈利能力指标
```python
# ROE (净资产收益率)
roe = net_income / shareholders_equity

# ROA (总资产收益率) 
roa = net_income / total_assets

# 毛利率
gross_margin = (revenue - cost_of_goods_sold) / revenue

# 净利率
net_margin = net_income / revenue

# EBITDA利润率
ebitda_margin = ebitda / revenue
```

#### 2.1.2 偿债能力指标
```python
# 流动比率
current_ratio = current_assets / current_liabilities

# 速动比率
quick_ratio = (current_assets - inventory) / current_liabilities

# 资产负债率
debt_ratio = total_liabilities / total_assets

# 利息保障倍数
interest_coverage = ebit / interest_expense
```

#### 2.1.3 运营效率指标
```python
# 总资产周转率
asset_turnover = revenue / total_assets

# 应收账款周转率
receivables_turnover = revenue / accounts_receivable

# 存货周转率
inventory_turnover = cost_of_goods_sold / inventory

# 现金转换周期
cash_conversion_cycle = days_sales_outstanding + days_inventory_outstanding - days_payable_outstanding
```

### 2.2 时间序列特征提取

#### 2.2.1 趋势特征
```python
# 收入增长率（同比）
revenue_growth_yoy = (revenue_current - revenue_previous_year) / revenue_previous_year

# 净利润增长率（同比）
profit_growth_yoy = (net_income_current - net_income_previous_year) / net_income_previous_year

# 3年复合增长率
cagr_3y = (value_current / value_3years_ago) ** (1/3) - 1

# 移动平均趋势
ma_trend = (current_value - moving_average_4q) / moving_average_4q
```

#### 2.2.2 波动性特征
```python
# 收入波动率（4个季度标准差）
revenue_volatility = np.std(revenue_last_4_quarters) / np.mean(revenue_last_4_quarters)

# 利润稳定性指标
profit_stability = 1 - (np.std(net_income_last_4_quarters) / np.mean(net_income_last_4_quarters))

# 现金流波动率
cashflow_volatility = np.std(operating_cashflow_last_4_quarters)
```

### 2.3 行业相对指标
```python
# 行业相对ROE
relative_roe = company_roe / industry_median_roe

# 行业相对估值
relative_pe = company_pe / industry_median_pe

# 行业排名百分位
industry_rank_percentile = company_rank / total_companies_in_industry
```

## 3. 数据预处理策略

### 3.1 缺失值处理
```python
def handle_missing_values(df):
    """处理缺失值的策略"""
    
    # 1. 前向填充（适用于连续性指标）
    continuous_cols = ['revenue', 'net_income', 'total_assets']
    df[continuous_cols] = df.groupby('ts_code')[continuous_cols].fillna(method='ffill')
    
    # 2. 行业中位数填充（适用于比率指标）
    ratio_cols = ['roe', 'roa', 'current_ratio']
    for col in ratio_cols:
        df[col] = df.groupby('industry')[col].transform(
            lambda x: x.fillna(x.median())
        )
    
    # 3. 零值填充（适用于特殊项目）
    special_cols = ['non_recurring_items', 'extraordinary_items']
    df[special_cols] = df[special_cols].fillna(0)
    
    return df
```

### 3.2 异常值检测与处理
```python
def detect_outliers(df, method='iqr'):
    """异常值检测"""
    
    if method == 'iqr':
        Q1 = df.quantile(0.25)
        Q3 = df.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        # 标记异常值
        outliers = (df < lower_bound) | (df > upper_bound)
        
    elif method == 'zscore':
        z_scores = np.abs(stats.zscore(df))
        outliers = z_scores > 3
    
    return outliers

def winsorize_outliers(df, percentile=0.01):
    """异常值截尾处理"""
    return df.clip(
        lower=df.quantile(percentile),
        upper=df.quantile(1-percentile),
        axis=0
    )
```

### 3.3 数据标准化
```python
from sklearn.preprocessing import StandardScaler, RobustScaler

def normalize_features(X_train, X_test, method='robust'):
    """特征标准化"""
    
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'robust':
        scaler = RobustScaler()  # 对异常值更鲁棒
    
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    return X_train_scaled, X_test_scaled, scaler
```

## 4. 机器学习模型选择

### 4.1 分类模型（涨跌预测）

#### 4.1.1 随机森林 (推荐)
```python
from sklearn.ensemble import RandomForestClassifier

def build_rf_model():
    """构建随机森林分类器"""
    model = RandomForestClassifier(
        n_estimators=500,
        max_depth=15,
        min_samples_split=20,
        min_samples_leaf=10,
        max_features='sqrt',
        random_state=42,
        class_weight='balanced'  # 处理类别不平衡
    )
    return model
```

**选择理由**：
- 能处理非线性关系
- 对异常值鲁棒
- 提供特征重要性
- 不需要特征缩放

#### 4.1.2 XGBoost (高性能选择)
```python
import xgboost as xgb

def build_xgb_model():
    """构建XGBoost分类器"""
    model = xgb.XGBClassifier(
        n_estimators=1000,
        max_depth=8,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=1.0,
        random_state=42,
        eval_metric='logloss'
    )
    return model
```

#### 4.1.3 神经网络 (深度学习)
```python
import tensorflow as tf
from tensorflow.keras import layers

def build_dnn_model(input_dim):
    """构建深度神经网络"""
    model = tf.keras.Sequential([
        layers.Dense(512, activation='relu', input_shape=(input_dim,)),
        layers.BatchNormalization(),
        layers.Dropout(0.3),
        
        layers.Dense(256, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.3),
        
        layers.Dense(128, activation='relu'),
        layers.Dropout(0.2),
        
        layers.Dense(64, activation='relu'),
        layers.Dropout(0.2),
        
        layers.Dense(1, activation='sigmoid')  # 二分类
    ])
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )
    
    return model
```

### 4.2 回归模型（收益率预测）

#### 4.2.1 支持向量回归
```python
from sklearn.svm import SVR

def build_svr_model():
    """构建支持向量回归模型"""
    model = SVR(
        kernel='rbf',
        C=1.0,
        gamma='scale',
        epsilon=0.01
    )
    return model
```

#### 4.2.2 LSTM时间序列模型
```python
def build_lstm_model(sequence_length, feature_dim):
    """构建LSTM时间序列模型"""
    model = tf.keras.Sequential([
        layers.LSTM(128, return_sequences=True, input_shape=(sequence_length, feature_dim)),
        layers.Dropout(0.2),
        
        layers.LSTM(64, return_sequences=True),
        layers.Dropout(0.2),
        
        layers.LSTM(32),
        layers.Dropout(0.2),
        
        layers.Dense(16, activation='relu'),
        layers.Dense(1)  # 回归输出
    ])
    
    model.compile(
        optimizer='adam',
        loss='mse',
        metrics=['mae']
    )
    
    return model
```

## 5. 向量化策略

### 5.1 特征向量构建
```python
def create_feature_vector(company_data, industry_data, market_data):
    """构建公司特征向量"""
    
    # 基础财务指标 (50维)
    financial_features = [
        'roe', 'roa', 'gross_margin', 'net_margin', 'current_ratio',
        'quick_ratio', 'debt_ratio', 'asset_turnover', 'inventory_turnover',
        'receivables_turnover', 'eps', 'bps', 'revenue_ps', 'cashflow_ps'
        # ... 更多指标
    ]
    
    # 增长性指标 (20维)
    growth_features = [
        'revenue_growth_1y', 'revenue_growth_3y', 'profit_growth_1y',
        'profit_growth_3y', 'asset_growth_1y', 'eps_growth_1y'
        # ... 更多增长指标
    ]
    
    # 行业相对指标 (15维)
    relative_features = [
        'relative_roe', 'relative_pe', 'relative_pb', 'industry_rank_roe',
        'industry_rank_growth', 'market_share'
        # ... 更多相对指标
    ]
    
    # 技术指标 (10维)
    technical_features = [
        'price_momentum_20d', 'price_momentum_60d', 'volatility_20d',
        'rsi_14d', 'volume_ratio'
        # ... 更多技术指标
    ]
    
    # 宏观经济指标 (5维)
    macro_features = [
        'gdp_growth', 'inflation_rate', 'interest_rate', 'market_sentiment'
    ]
    
    # 合并所有特征 (总计约100维)
    feature_vector = np.concatenate([
        financial_features, growth_features, relative_features,
        technical_features, macro_features
    ])
    
    return feature_vector
```

### 5.2 向量数据库集成方案

#### 5.2.1 使用Chroma向量数据库
```python
import chromadb
from chromadb.config import Settings

class FinancialVectorDB:
    def __init__(self, persist_directory="./financial_vectors"):
        """初始化向量数据库"""
        self.client = chromadb.PersistentClient(path=persist_directory)
        self.collection = self.client.get_or_create_collection(
            name="financial_features",
            metadata={"description": "Financial features for stock prediction"}
        )
    
    def add_company_vectors(self, company_codes, feature_vectors, metadata):
        """添加公司特征向量"""
        self.collection.add(
            ids=company_codes,
            embeddings=feature_vectors.tolist(),
            metadatas=metadata
        )
    
    def query_similar_companies(self, target_vector, n_results=10):
        """查询相似公司"""
        results = self.collection.query(
            query_embeddings=[target_vector.tolist()],
            n_results=n_results,
            include=['metadatas', 'distances']
        )
        return results
    
    def update_company_vector(self, company_code, new_vector, new_metadata):
        """更新公司向量"""
        self.collection.update(
            ids=[company_code],
            embeddings=[new_vector.tolist()],
            metadatas=[new_metadata]
        )
```

## 6. 数学模型与概率分析

### 6.1 基于财务指标的概率模型

#### 6.1.1 多元正态分布模型
```python
from scipy.stats import multivariate_normal

def financial_probability_model(financial_features):
    """基于财务指标的多元概率模型"""
    
    # 计算协方差矩阵
    cov_matrix = np.cov(financial_features.T)
    mean_vector = np.mean(financial_features, axis=0)
    
    # 构建多元正态分布
    mvn = multivariate_normal(mean=mean_vector, cov=cov_matrix)
    
    def calculate_probability(new_features):
        """计算新样本的概率密度"""
        return mvn.pdf(new_features)
    
    def calculate_percentile(new_features):
        """计算新样本在历史分布中的百分位"""
        prob_density = mvn.pdf(new_features)
        historical_densities = mvn.pdf(financial_features)
        percentile = (historical_densities < prob_density).mean()
        return percentile
    
    return calculate_probability, calculate_percentile
```

#### 6.1.2 贝叶斯分类模型
```python
def bayesian_stock_prediction(financial_ratios, historical_returns):
    """贝叶斯股票预测模型"""
    
    # P(涨|财务指标) = P(财务指标|涨) * P(涨) / P(财务指标)
    
    # 先验概率
    p_up = (historical_returns > 0).mean()
    p_down = 1 - p_up
    
    # 似然函数（基于历史数据拟合）
    up_samples = financial_ratios[historical_returns > 0]
    down_samples = financial_ratios[historical_returns <= 0]
    
    # 使用核密度估计
    from scipy.stats import gaussian_kde
    
    kde_up = gaussian_kde(up_samples.T)
    kde_down = gaussian_kde(down_samples.T)
    
    def predict_probability(new_ratios):
        """预测上涨概率"""
        likelihood_up = kde_up(new_ratios.reshape(-1, 1))[0]
        likelihood_down = kde_down(new_ratios.reshape(-1, 1))[0]
        
        # 贝叶斯公式
        posterior_up = (likelihood_up * p_up) / (likelihood_up * p_up + likelihood_down * p_down)
        
        return posterior_up
    
    return predict_probability
```

### 6.2 风险评估模型

#### 6.2.1 VaR计算（基于财务数据）
```python
def calculate_fundamental_var(financial_features, returns, confidence_level=0.05):
    """基于基本面的VaR计算"""
    
    # 使用回归模型预测收益率
    from sklearn.linear_model import LinearRegression
    
    model = LinearRegression()
    model.fit(financial_features, returns)
    
    # 预测收益率分布
    predicted_returns = model.predict(financial_features)
    residuals = returns - predicted_returns
    
    # 计算VaR
    var = np.percentile(residuals, confidence_level * 100)
    
    return var, model
```

## 7. Python软件架构设计

### 7.1 项目结构
```
stock_prediction/
├── data/
│   ├── raw/                 # 原始Excel数据
│   ├── processed/           # 处理后的数据
│   └── vectors/            # 向量数据库
├── src/
│   ├── data_loader.py      # 数据加载模块
│   ├── feature_engineer.py # 特征工程模块
│   ├── models/             # 模型模块
│   │   ├── __init__.py
│   │   ├── classification.py
│   │   ├── regression.py
│   │   └── time_series.py
│   ├── vector_db.py        # 向量数据库模块
│   ├── preprocessor.py     # 数据预处理模块
│   └── utils.py           # 工具函数
├── notebooks/              # Jupyter分析笔记本
├── tests/                  # 单元测试
├── config/                 # 配置文件
└── main.py                # 主程序入口
```

### 7.2 核心模块设计

#### 7.2.1 数据加载模块
```python
class FinancialDataLoader:
    """财务数据加载器"""
    
    def __init__(self, data_path="docs_markdown"):
        self.data_path = data_path
        self.stock_list = None
        self.company_info = None
        
    def load_stock_list(self):
        """加载股票列表"""
        # 从股票列表.md解析数据
        pass
    
    def load_financial_data(self, year_range=(2010, 2020)):
        """加载指定年份范围的财务数据"""
        financial_data = {}
        
        for year in range(year_range[0], year_range[1] + 1):
            # 加载利润表
            profit_data = self._load_markdown_table(f"{year}利润表.md")
            
            # 加载资产负债表
            balance_data = self._load_markdown_table(f"{year}资产负债表.md")
            
            # 加载现金流量表
            cashflow_data = self._load_markdown_table(f"{year}现金流量表.md")
            
            # 加载财务指标表
            ratio_data = self._load_markdown_table(f"{year}财务指标表.md")
            
            financial_data[year] = {
                'profit': profit_data,
                'balance': balance_data,
                'cashflow': cashflow_data,
                'ratios': ratio_data
            }
        
        return financial_data
    
    def _load_markdown_table(self, filename):
        """从Markdown文件加载表格数据"""
        import pandas as pd
        import re

        filepath = os.path.join(self.data_path, filename)
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取表格部分
        lines = content.split('\n')
        table_lines = []
        in_table = False

        for line in lines:
            if '|' in line and '---' in line:
                in_table = True
                continue
            elif '|' in line and in_table:
                table_lines.append(line)
            elif in_table and '|' not in line:
                break

        if not table_lines:
            return pd.DataFrame()

        # 解析表格
        data = []
        headers = None

        for i, line in enumerate(table_lines):
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if i == 0:
                headers = cells
            else:
                data.append(cells)

        df = pd.DataFrame(data, columns=headers)
        return df

#### 7.2.2 特征工程模块
```python
class FinancialFeatureEngineer:
    """财务特征工程器"""

    def __init__(self):
        self.feature_names = []
        self.scalers = {}

    def create_financial_ratios(self, profit_df, balance_df, cashflow_df):
        """创建财务比率特征"""
        features = pd.DataFrame()

        # 盈利能力指标
        features['roe'] = profit_df['n_income'] / balance_df['total_hldr_eqy_exc_min_int']
        features['roa'] = profit_df['n_income'] / balance_df['total_assets']
        features['gross_margin'] = (profit_df['revenue'] - profit_df['oper_cost']) / profit_df['revenue']
        features['net_margin'] = profit_df['n_income'] / profit_df['revenue']

        # 偿债能力指标
        features['current_ratio'] = balance_df['total_cur_assets'] / balance_df['total_cur_liab']
        features['quick_ratio'] = (balance_df['total_cur_assets'] - balance_df['inventories']) / balance_df['total_cur_liab']
        features['debt_ratio'] = balance_df['total_liab'] / balance_df['total_assets']

        # 运营效率指标
        features['asset_turnover'] = profit_df['revenue'] / balance_df['total_assets']
        features['inventory_turnover'] = profit_df['oper_cost'] / balance_df['inventories']

        # 现金流指标
        features['ocf_to_revenue'] = cashflow_df['n_cashflow_act'] / profit_df['revenue']
        features['ocf_to_netincome'] = cashflow_df['n_cashflow_act'] / profit_df['n_income']

        return features

    def create_time_series_features(self, data_dict, lookback_periods=[1, 2, 3, 4]):
        """创建时间序列特征"""
        ts_features = pd.DataFrame()

        for period in lookback_periods:
            # 增长率特征
            ts_features[f'revenue_growth_{period}y'] = self._calculate_growth_rate(
                data_dict, 'revenue', period
            )
            ts_features[f'profit_growth_{period}y'] = self._calculate_growth_rate(
                data_dict, 'n_income', period
            )

            # 波动性特征
            ts_features[f'revenue_volatility_{period}y'] = self._calculate_volatility(
                data_dict, 'revenue', period
            )

        return ts_features

    def _calculate_growth_rate(self, data_dict, metric, years_back):
        """计算增长率"""
        current_year = max(data_dict.keys())
        past_year = current_year - years_back

        if past_year not in data_dict:
            return None

        current_value = data_dict[current_year]['profit'][metric]
        past_value = data_dict[past_year]['profit'][metric]

        growth_rate = (current_value - past_value) / past_value
        return growth_rate

    def create_industry_features(self, company_data, industry_benchmarks):
        """创建行业相对特征"""
        industry_features = pd.DataFrame()

        # 行业相对指标
        industry_features['relative_roe'] = company_data['roe'] / industry_benchmarks['roe_median']
        industry_features['relative_growth'] = company_data['revenue_growth_1y'] / industry_benchmarks['growth_median']
        industry_features['industry_rank_roe'] = company_data['roe'].rank(pct=True)

        return industry_features

#### 7.2.3 模型训练模块
```python
class StockPredictionModel:
    """股票预测模型"""

    def __init__(self, model_type='random_forest'):
        self.model_type = model_type
        self.model = None
        self.feature_importance = None
        self.performance_metrics = {}

    def prepare_training_data(self, financial_data, price_data, prediction_horizon=30):
        """准备训练数据"""
        X = []  # 特征矩阵
        y = []  # 标签（涨跌或收益率）

        for company_code in financial_data.keys():
            company_features = self._extract_company_features(
                financial_data[company_code]
            )

            # 获取未来收益率作为标签
            future_return = self._get_future_return(
                company_code, price_data, prediction_horizon
            )

            if future_return is not None:
                X.append(company_features)
                y.append(future_return)

        return np.array(X), np.array(y)

    def train_classification_model(self, X_train, y_train):
        """训练分类模型（涨跌预测）"""
        # 将连续收益率转换为分类标签
        y_binary = (y_train > 0).astype(int)

        if self.model_type == 'random_forest':
            self.model = RandomForestClassifier(
                n_estimators=500,
                max_depth=15,
                min_samples_split=20,
                random_state=42
            )
        elif self.model_type == 'xgboost':
            self.model = xgb.XGBClassifier(
                n_estimators=1000,
                max_depth=8,
                learning_rate=0.05,
                random_state=42
            )

        self.model.fit(X_train, y_binary)

        # 保存特征重要性
        if hasattr(self.model, 'feature_importances_'):
            self.feature_importance = self.model.feature_importances_

    def train_regression_model(self, X_train, y_train):
        """训练回归模型（收益率预测）"""
        if self.model_type == 'svr':
            self.model = SVR(kernel='rbf', C=1.0, gamma='scale')
        elif self.model_type == 'neural_network':
            self.model = self._build_neural_network(X_train.shape[1])

        self.model.fit(X_train, y_train)

    def predict(self, X_test):
        """模型预测"""
        if self.model is None:
            raise ValueError("模型尚未训练")

        predictions = self.model.predict(X_test)

        # 如果是分类模型，同时返回概率
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(X_test)
            return predictions, probabilities

        return predictions

    def evaluate_model(self, X_test, y_test):
        """模型评估"""
        predictions = self.predict(X_test)

        if self.model_type in ['random_forest', 'xgboost']:
            # 分类指标
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

            y_binary = (y_test > 0).astype(int)
            pred_binary = predictions[0] if isinstance(predictions, tuple) else predictions

            self.performance_metrics = {
                'accuracy': accuracy_score(y_binary, pred_binary),
                'precision': precision_score(y_binary, pred_binary),
                'recall': recall_score(y_binary, pred_binary),
                'f1_score': f1_score(y_binary, pred_binary)
            }
        else:
            # 回归指标
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

            self.performance_metrics = {
                'mse': mean_squared_error(y_test, predictions),
                'mae': mean_absolute_error(y_test, predictions),
                'r2': r2_score(y_test, predictions)
            }

        return self.performance_metrics

## 8. 实施步骤与代码框架

### 8.1 完整实施流程

#### 步骤1：数据预处理与特征工程
```python
# main.py - 主程序入口
import pandas as pd
import numpy as np
from src.data_loader import FinancialDataLoader
from src.feature_engineer import FinancialFeatureEngineer
from src.preprocessor import DataPreprocessor
from src.models.classification import StockClassificationModel
from src.vector_db import FinancialVectorDB

def main():
    """主程序流程"""

    # 1. 数据加载
    print("正在加载财务数据...")
    loader = FinancialDataLoader("docs_markdown")
    stock_list = loader.load_stock_list()
    financial_data = loader.load_financial_data(year_range=(2010, 2020))
    company_info = loader.load_company_info()

    # 2. 特征工程
    print("正在进行特征工程...")
    feature_engineer = FinancialFeatureEngineer()

    all_features = []
    all_labels = []

    for year in range(2010, 2020):  # 2020年数据用于预测2021年
        # 提取当年财务特征
        year_features = feature_engineer.extract_annual_features(
            financial_data[year], company_info
        )

        # 计算下一年的收益率标签
        next_year_returns = calculate_next_year_returns(year + 1)

        all_features.append(year_features)
        all_labels.append(next_year_returns)

    # 合并所有年份数据
    X = pd.concat(all_features, ignore_index=True)
    y = pd.concat(all_labels, ignore_index=True)

    # 3. 数据预处理
    print("正在进行数据预处理...")
    preprocessor = DataPreprocessor()
    X_processed = preprocessor.fit_transform(X)

    # 4. 训练测试集分割
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        X_processed, y, test_size=0.2, random_state=42, stratify=(y > 0)
    )

    # 5. 模型训练
    print("正在训练模型...")
    model = StockClassificationModel(model_type='random_forest')
    model.train(X_train, y_train)

    # 6. 模型评估
    print("正在评估模型...")
    performance = model.evaluate(X_test, y_test)
    print(f"模型性能: {performance}")

    # 7. 向量数据库存储
    print("正在存储特征向量...")
    vector_db = FinancialVectorDB()
    vector_db.store_features(X_processed, stock_list, financial_data)

    # 8. 预测2021年
    print("正在预测2021年股票表现...")
    latest_features = feature_engineer.extract_annual_features(
        financial_data[2020], company_info
    )
    predictions = model.predict(latest_features)

    # 保存预测结果
    results_df = pd.DataFrame({
        'ts_code': stock_list['ts_code'],
        'company_name': stock_list['name'],
        'predicted_direction': predictions,
        'prediction_probability': model.predict_proba(latest_features)[:, 1]
    })

    results_df.to_csv('predictions_2021.csv', index=False)
    print("预测完成，结果已保存到 predictions_2021.csv")

if __name__ == "__main__":
    main()
```

#### 步骤2：特征工程实现
```python
# src/feature_engineer.py
class FinancialFeatureEngineer:
    """财务特征工程实现"""

    def extract_annual_features(self, year_data, company_info):
        """提取年度特征"""
        profit_df = year_data['profit']
        balance_df = year_data['balance']
        cashflow_df = year_data['cashflow']
        ratio_df = year_data['ratios']

        features = pd.DataFrame()

        # 1. 基础财务比率
        features = pd.concat([features, self._basic_ratios(profit_df, balance_df)], axis=1)

        # 2. 现金流特征
        features = pd.concat([features, self._cashflow_features(cashflow_df, profit_df)], axis=1)

        # 3. 增长性特征
        features = pd.concat([features, self._growth_features(profit_df, balance_df)], axis=1)

        # 4. 质量特征
        features = pd.concat([features, self._quality_features(profit_df, balance_df, cashflow_df)], axis=1)

        # 5. 行业特征
        features = pd.concat([features, self._industry_features(company_info)], axis=1)

        return features

    def _basic_ratios(self, profit_df, balance_df):
        """基础财务比率"""
        ratios = pd.DataFrame()

        # 盈利能力
        ratios['roe'] = profit_df['n_income'] / balance_df['total_hldr_eqy_exc_min_int']
        ratios['roa'] = profit_df['n_income'] / balance_df['total_assets']
        ratios['gross_margin'] = (profit_df['revenue'] - profit_df['oper_cost']) / profit_df['revenue']
        ratios['ebitda_margin'] = profit_df['ebitda'] / profit_df['revenue']

        # 偿债能力
        ratios['current_ratio'] = balance_df['total_cur_assets'] / balance_df['total_cur_liab']
        ratios['debt_to_equity'] = balance_df['total_liab'] / balance_df['total_hldr_eqy_exc_min_int']
        ratios['interest_coverage'] = profit_df['ebit'] / profit_df['fin_exp']

        # 运营效率
        ratios['asset_turnover'] = profit_df['revenue'] / balance_df['total_assets']
        ratios['inventory_turnover'] = profit_df['oper_cost'] / balance_df['inventories']
        ratios['receivables_turnover'] = profit_df['revenue'] / balance_df['accounts_receiv']

        return ratios

    def _quality_features(self, profit_df, balance_df, cashflow_df):
        """盈利质量特征"""
        quality = pd.DataFrame()

        # 盈利质量
        quality['accruals'] = (profit_df['n_income'] - cashflow_df['n_cashflow_act']) / balance_df['total_assets']
        quality['earnings_quality'] = cashflow_df['n_cashflow_act'] / profit_df['n_income']

        # 资产质量
        quality['tangible_book_value'] = (balance_df['total_hldr_eqy_exc_min_int'] - balance_df['intang_assets']) / balance_df['total_assets']

        return quality

#### 步骤3：模型集成与预测
```python
# src/models/ensemble.py
class EnsembleStockPredictor:
    """集成模型预测器"""

    def __init__(self):
        self.models = {}
        self.weights = {}

    def add_model(self, name, model, weight=1.0):
        """添加子模型"""
        self.models[name] = model
        self.weights[name] = weight

    def train_ensemble(self, X_train, y_train, X_val, y_val):
        """训练集成模型"""

        # 训练各个子模型
        for name, model in self.models.items():
            print(f"训练模型: {name}")
            model.fit(X_train, y_train)

        # 基于验证集优化权重
        self._optimize_weights(X_val, y_val)

    def _optimize_weights(self, X_val, y_val):
        """优化模型权重"""
        from scipy.optimize import minimize

        def objective(weights):
            ensemble_pred = self._weighted_prediction(X_val, weights)
            mse = np.mean((ensemble_pred - y_val) ** 2)
            return mse

        # 约束条件：权重和为1
        constraints = {'type': 'eq', 'fun': lambda w: np.sum(w) - 1}
        bounds = [(0, 1) for _ in range(len(self.models))]

        result = minimize(
            objective,
            x0=np.ones(len(self.models)) / len(self.models),
            bounds=bounds,
            constraints=constraints
        )

        # 更新权重
        model_names = list(self.models.keys())
        for i, name in enumerate(model_names):
            self.weights[name] = result.x[i]

    def predict(self, X):
        """集成预测"""
        predictions = {}

        for name, model in self.models.items():
            predictions[name] = model.predict(X)

        # 加权平均
        ensemble_pred = np.zeros(len(X))
        for name, pred in predictions.items():
            ensemble_pred += self.weights[name] * pred

        return ensemble_pred
```

### 8.2 配置文件设计
```python
# config/model_config.py
MODEL_CONFIG = {
    'random_forest': {
        'n_estimators': 500,
        'max_depth': 15,
        'min_samples_split': 20,
        'min_samples_leaf': 10,
        'max_features': 'sqrt',
        'random_state': 42
    },

    'xgboost': {
        'n_estimators': 1000,
        'max_depth': 8,
        'learning_rate': 0.05,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_alpha': 0.1,
        'reg_lambda': 1.0,
        'random_state': 42
    },

    'neural_network': {
        'hidden_layers': [512, 256, 128, 64],
        'dropout_rate': 0.3,
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 100
    }
}

FEATURE_CONFIG = {
    'financial_ratios': [
        'roe', 'roa', 'gross_margin', 'net_margin', 'ebitda_margin',
        'current_ratio', 'quick_ratio', 'debt_to_equity', 'asset_turnover'
    ],

    'growth_features': [
        'revenue_growth_1y', 'revenue_growth_3y', 'profit_growth_1y',
        'eps_growth_1y', 'asset_growth_1y'
    ],

    'quality_features': [
        'accruals', 'earnings_quality', 'tangible_book_value',
        'working_capital_ratio'
    ],

    'industry_features': [
        'relative_roe', 'relative_growth', 'industry_rank_roe',
        'market_share', 'industry_beta'
    ]
}

## 9. 向量数据库详细实现

### 9.1 向量存储与检索
```python
# src/vector_db.py
import chromadb
import numpy as np
import pandas as pd
from typing import List, Dict, Any

class FinancialVectorDatabase:
    """财务数据向量数据库"""

    def __init__(self, persist_directory="./data/vectors"):
        self.client = chromadb.PersistentClient(path=persist_directory)
        self.collections = {}
        self._initialize_collections()

    def _initialize_collections(self):
        """初始化向量集合"""
        # 公司特征向量集合
        self.collections['companies'] = self.client.get_or_create_collection(
            name="company_features",
            metadata={"description": "Company financial feature vectors"}
        )

        # 行业特征向量集合
        self.collections['industries'] = self.client.get_or_create_collection(
            name="industry_features",
            metadata={"description": "Industry benchmark vectors"}
        )

        # 时间序列特征集合
        self.collections['timeseries'] = self.client.get_or_create_collection(
            name="timeseries_features",
            metadata={"description": "Time series financial features"}
        )

    def store_company_vectors(self, company_features: pd.DataFrame, metadata: List[Dict]):
        """存储公司特征向量"""

        # 转换为向量格式
        vectors = company_features.values.tolist()
        ids = [f"{row['ts_code']}_{row['year']}" for row in metadata]

        self.collections['companies'].add(
            ids=ids,
            embeddings=vectors,
            metadatas=metadata
        )

    def query_similar_companies(self, target_vector: np.ndarray,
                              filters: Dict = None, n_results: int = 10):
        """查询相似公司"""

        where_clause = filters if filters else {}

        results = self.collections['companies'].query(
            query_embeddings=[target_vector.tolist()],
            n_results=n_results,
            where=where_clause,
            include=['metadatas', 'distances', 'embeddings']
        )

        return results

    def get_industry_benchmark(self, industry: str, year: int):
        """获取行业基准向量"""
        results = self.collections['industries'].query(
            query_embeddings=None,
            where={"industry": industry, "year": year},
            n_results=1
        )

        if results['embeddings']:
            return np.array(results['embeddings'][0])
        return None

### 9.2 相似性分析
```python
def find_similar_investment_opportunities(target_company: str,
                                        vector_db: FinancialVectorDatabase,
                                        top_k: int = 20):
    """寻找相似的投资机会"""

    # 获取目标公司最新特征向量
    target_vector = vector_db.get_company_vector(target_company, year=2020)

    if target_vector is None:
        return None

    # 查询相似公司
    similar_companies = vector_db.query_similar_companies(
        target_vector=target_vector,
        filters={"year": 2020},  # 只查询最新年份
        n_results=top_k
    )

    # 分析相似性特征
    similarity_analysis = []

    for i, company in enumerate(similar_companies['metadatas']):
        similarity_score = 1 - similar_companies['distances'][i]  # 转换为相似度

        analysis = {
            'ts_code': company['ts_code'],
            'company_name': company['name'],
            'industry': company['industry'],
            'similarity_score': similarity_score,
            'key_similarities': analyze_feature_similarity(
                target_vector,
                np.array(similar_companies['embeddings'][i])
            )
        }

        similarity_analysis.append(analysis)

    return similarity_analysis

def analyze_feature_similarity(vector1: np.ndarray, vector2: np.ndarray,
                             feature_names: List[str]):
    """分析特征相似性"""

    # 计算各个特征的相似度
    feature_similarities = 1 - np.abs(vector1 - vector2) / (np.abs(vector1) + np.abs(vector2) + 1e-8)

    # 找出最相似的特征
    top_similar_indices = np.argsort(feature_similarities)[-5:]

    key_similarities = []
    for idx in top_similar_indices:
        key_similarities.append({
            'feature': feature_names[idx],
            'similarity': feature_similarities[idx],
            'target_value': vector1[idx],
            'similar_value': vector2[idx]
        })

    return key_similarities
```

## 10. 模型评估与验证

### 10.1 评估指标体系
```python
def comprehensive_evaluation(y_true, y_pred, y_prob=None):
    """综合评估指标"""
    from sklearn.metrics import (
        accuracy_score, precision_score, recall_score, f1_score,
        roc_auc_score, confusion_matrix, classification_report
    )

    # 基础分类指标
    metrics = {
        'accuracy': accuracy_score(y_true, y_pred),
        'precision': precision_score(y_true, y_pred),
        'recall': recall_score(y_true, y_pred),
        'f1_score': f1_score(y_true, y_pred)
    }

    # ROC-AUC (需要概率预测)
    if y_prob is not None:
        metrics['auc'] = roc_auc_score(y_true, y_prob)

    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    metrics['confusion_matrix'] = cm

    # 财务特定指标
    metrics.update(calculate_financial_metrics(y_true, y_pred, y_prob))

    return metrics

def calculate_financial_metrics(y_true, y_pred, y_prob):
    """计算财务特定的评估指标"""

    # 投资组合收益率（假设等权重投资预测为涨的股票）
    predicted_up = (y_pred == 1)
    actual_returns = y_true  # 假设y_true包含实际收益率

    portfolio_return = np.mean(actual_returns[predicted_up]) if np.any(predicted_up) else 0

    # 夏普比率
    risk_free_rate = 0.03  # 假设无风险利率3%
    portfolio_volatility = np.std(actual_returns[predicted_up]) if np.any(predicted_up) else 0
    sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0

    # 最大回撤
    cumulative_returns = np.cumprod(1 + actual_returns[predicted_up])
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = np.min(drawdown)

    # 信息比率
    benchmark_return = np.mean(actual_returns)  # 市场平均收益作为基准
    excess_return = portfolio_return - benchmark_return
    tracking_error = np.std(actual_returns[predicted_up] - benchmark_return)
    information_ratio = excess_return / tracking_error if tracking_error > 0 else 0

    return {
        'portfolio_return': portfolio_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'information_ratio': information_ratio,
        'hit_rate': accuracy_score(y_true > 0, y_pred)  # 方向预测准确率
    }
```

### 10.2 回测框架
```python
def backtest_strategy(model, financial_data, price_data, start_year=2015, end_year=2020):
    """回测交易策略"""

    results = []

    for year in range(start_year, end_year):
        # 使用当年数据训练模型
        train_data = prepare_training_data(financial_data, year-3, year-1)
        model.fit(train_data['X'], train_data['y'])

        # 预测下一年
        test_features = extract_features(financial_data[year])
        predictions = model.predict(test_features)
        probabilities = model.predict_proba(test_features)

        # 构建投资组合（选择预测概率最高的前20%股票）
        top_stocks = select_top_stocks(predictions, probabilities, percentile=0.8)

        # 计算投资组合表现
        portfolio_performance = calculate_portfolio_performance(
            top_stocks, price_data, year+1
        )

        results.append({
            'year': year+1,
            'portfolio_return': portfolio_performance['return'],
            'benchmark_return': portfolio_performance['benchmark'],
            'excess_return': portfolio_performance['excess'],
            'volatility': portfolio_performance['volatility'],
            'sharpe_ratio': portfolio_performance['sharpe'],
            'max_drawdown': portfolio_performance['max_drawdown']
        })

    return pd.DataFrame(results)

## 11. 实施建议与最佳实践

### 11.1 分阶段实施计划

#### 阶段1：数据基础建设（2-3周）
1. **数据清洗与验证**
   - 验证Excel转Markdown转换的准确性
   - 处理数据格式不一致问题
   - 建立数据质量检查机制

2. **基础特征工程**
   - 实现核心财务比率计算
   - 建立行业分类体系
   - 创建时间序列特征

#### 阶段2：模型开发（3-4周）
1. **基线模型建立**
   - 实现随机森林分类器
   - 建立基础评估框架
   - 完成简单回测验证

2. **模型优化**
   - 超参数调优
   - 特征选择优化
   - 集成模型开发

#### 阶段3：系统集成（2-3周）
1. **向量数据库集成**
   - 部署Chroma向量数据库
   - 实现相似性搜索功能
   - 建立实时更新机制

2. **生产环境部署**
   - API接口开发
   - 监控告警系统
   - 性能优化

### 11.2 关键技术要点

#### 11.2.1 特征选择策略
```python
def feature_selection_pipeline(X, y, method='recursive'):
    """特征选择流水线"""

    if method == 'recursive':
        from sklearn.feature_selection import RFE
        from sklearn.ensemble import RandomForestClassifier

        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
        selector = RFE(estimator, n_features_to_select=50, step=1)
        X_selected = selector.fit_transform(X, y)

        return X_selected, selector.support_

    elif method == 'mutual_info':
        from sklearn.feature_selection import mutual_info_classif

        mi_scores = mutual_info_classif(X, y, random_state=42)
        top_features = np.argsort(mi_scores)[-50:]  # 选择前50个特征

        return X[:, top_features], top_features

    elif method == 'correlation':
        # 移除高相关性特征
        corr_matrix = np.corrcoef(X.T)
        high_corr_pairs = np.where(np.abs(corr_matrix) > 0.95)

        features_to_remove = set()
        for i, j in zip(high_corr_pairs[0], high_corr_pairs[1]):
            if i != j and i not in features_to_remove:
                features_to_remove.add(j)

        remaining_features = [i for i in range(X.shape[1]) if i not in features_to_remove]

        return X[:, remaining_features], remaining_features
```

#### 11.2.2 模型解释性
```python
def explain_predictions(model, X_test, feature_names):
    """模型预测解释"""
    import shap

    # SHAP解释器
    if hasattr(model, 'predict_proba'):
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_test)

        # 特征重要性排序
        feature_importance = np.abs(shap_values).mean(0)
        importance_ranking = sorted(
            zip(feature_names, feature_importance),
            key=lambda x: x[1], reverse=True
        )

        return {
            'shap_values': shap_values,
            'feature_importance': importance_ranking,
            'explainer': explainer
        }

    return None

def generate_prediction_report(company_code, prediction, probability,
                             feature_values, explanation):
    """生成预测报告"""

    report = {
        'company_code': company_code,
        'prediction': '上涨' if prediction == 1 else '下跌',
        'confidence': probability,
        'key_factors': explanation['feature_importance'][:10],
        'risk_assessment': assess_prediction_risk(probability, feature_values),
        'similar_companies': find_historical_analogs(company_code, feature_values)
    }

    return report
```

### 11.3 风险控制机制

#### 11.3.1 模型风险监控
```python
def monitor_model_performance(model, recent_data, performance_threshold=0.6):
    """监控模型性能"""

    # 计算最近预测准确率
    recent_accuracy = calculate_recent_accuracy(model, recent_data)

    # 特征分布漂移检测
    feature_drift = detect_feature_drift(recent_data, historical_data)

    # 预测置信度分析
    confidence_analysis = analyze_prediction_confidence(model, recent_data)

    alerts = []

    if recent_accuracy < performance_threshold:
        alerts.append(f"模型准确率下降至 {recent_accuracy:.2%}")

    if feature_drift['max_drift'] > 0.1:
        alerts.append(f"特征分布发生显著漂移: {feature_drift['drifted_features']}")

    if confidence_analysis['low_confidence_ratio'] > 0.3:
        alerts.append(f"低置信度预测比例过高: {confidence_analysis['low_confidence_ratio']:.2%}")

    return {
        'status': 'warning' if alerts else 'normal',
        'alerts': alerts,
        'metrics': {
            'recent_accuracy': recent_accuracy,
            'feature_drift': feature_drift,
            'confidence_analysis': confidence_analysis
        }
    }
```

## 12. 部署与运维

### 12.1 API接口设计
```python
from flask import Flask, request, jsonify
import joblib

app = Flask(__name__)

# 加载训练好的模型
model = joblib.load('models/stock_prediction_model.pkl')
feature_engineer = joblib.load('models/feature_engineer.pkl')
vector_db = FinancialVectorDatabase()

@app.route('/predict', methods=['POST'])
def predict_stock():
    """股票预测API"""
    try:
        data = request.json
        company_code = data['ts_code']

        # 提取特征
        features = feature_engineer.extract_features(data['financial_data'])

        # 模型预测
        prediction = model.predict([features])[0]
        probability = model.predict_proba([features])[0]

        # 生成解释
        explanation = explain_predictions(model, [features], feature_engineer.feature_names)

        # 查找相似公司
        similar_companies = vector_db.query_similar_companies(features, n_results=5)

        response = {
            'ts_code': company_code,
            'prediction': int(prediction),
            'probability': {
                'down': float(probability[0]),
                'up': float(probability[1])
            },
            'confidence': float(max(probability)),
            'key_factors': explanation['feature_importance'][:5],
            'similar_companies': similar_companies['metadatas']
        }

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/similar', methods=['POST'])
def find_similar():
    """查找相似公司API"""
    try:
        data = request.json
        target_vector = np.array(data['features'])

        results = vector_db.query_similar_companies(
            target_vector,
            filters=data.get('filters', {}),
            n_results=data.get('n_results', 10)
        )

        return jsonify(results)

    except Exception as e:
        return jsonify({'error': str(e)}), 400

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
```

### 12.2 监控与维护
```python
def setup_monitoring():
    """设置监控系统"""

    # 数据质量监控
    def monitor_data_quality():
        # 检查数据完整性
        # 检查数据时效性
        # 检查异常值比例
        pass

    # 模型性能监控
    def monitor_model_performance():
        # 预测准确率趋势
        # 特征重要性变化
        # 预测置信度分布
        pass

    # 系统性能监控
    def monitor_system_performance():
        # API响应时间
        # 数据库查询性能
        # 内存使用情况
        pass

## 13. 总结与建议

### 13.1 技术优势
1. **数据丰富性**：11年完整财务数据，覆盖4,391家公司
2. **特征全面性**：325个财务指标，支持多维度分析
3. **模型多样性**：集成多种机器学习算法
4. **可解释性**：提供预测解释和相似性分析

### 13.2 实施建议（更新：采用混合架构）
1. **优先级排序**：
   - 高优先级：数据清洗、基础特征工程、随机森林模型、部署向量数据库与相似性搜索
   - 中优先级：SQL筛选与报表、模式识别、API接口、监控告警
   - 低优先级：深度学习模型、高级特征工程与可视化

2. **风险控制**：
   - 建立严格的数据验证机制
   - 实施渐进式模型部署（影子发布/灰度）
   - 设置性能与漂移监控告警

3. **持续优化**：
   - 定期重训练模型，滚动窗口评估
   - 持续收集反馈数据以改进推荐
   - 优化特征工程策略与向量检索参数（度量、索引）

### 13.3 预期效果
- **预测准确率**：目标达到65-70%（显著优于随机猜测的50%）
- **夏普比率**：目标达到1.5以上
- **最大回撤**：控制在15%以内
- **信息比率**：目标达到0.8以上

## 向量化技术选型分析

### 向量化需求评估

#### 数据规模分析
```python
# 我们的数据规模
companies = 4391
features = 325  # 财务指标
years = 11
total_records = companies * years  # 约48,301条记录
feature_matrix_size = total_records * features  # 约1,570万个数据点
```

#### 向量化的必要性分析

**在我们的财务数据分析场景中，向量化是高度推荐但不是绝对必要的**。基于数据规模和应用需求分析：

**向量化带来的具体优势：**

1. **高维相似性搜索效率**
```python
# 传统方法：O(n*m) 复杂度
def traditional_similarity_search(target_company, all_companies, features):
    similarities = []
    for company in all_companies:
        similarity = 0
        for feature in features:
            similarity += abs(target_company[feature] - company[feature])
        similarities.append(similarity)
    return sorted(similarities)  # 需要遍历所有公司的所有特征

# 向量化方法：O(log n) 复杂度（使用近似最近邻）
def vector_similarity_search(target_vector, vector_db):
    return vector_db.query(target_vector, n_results=10)  # 亚秒级查询
```

2. **复杂模式识别能力**
```python
# 325维财务特征的复杂关系
financial_vector = np.array([
    roe, roa, debt_ratio, current_ratio, # 基础比率
    revenue_growth_1y, revenue_growth_3y, # 增长指标
    industry_rank, market_share,         # 相对指标
    # ... 325个特征
])

# 向量化能捕捉非线性特征组合
# 例如：高ROE + 低债务比率 + 稳定增长 = 优质投资标的
```

#### 高维特征空间的挑战
```python
def demonstrate_curse_of_dimensionality():
    """演示维度诅咒问题"""

    # 在325维空间中，传统距离度量失效
    # 所有点之间的距离趋于相等

    import numpy as np
    from scipy.spatial.distance import pdist

    # 模拟325维财务特征
    n_companies = 1000
    n_features = 325

    # 随机生成财务数据
    financial_data = np.random.randn(n_companies, n_features)

    # 计算所有公司间的欧氏距离
    distances = pdist(financial_data)

    print(f"距离标准差: {np.std(distances):.4f}")
    print(f"距离均值: {np.mean(distances):.4f}")
    print(f"变异系数: {np.std(distances)/np.mean(distances):.4f}")

    # 在高维空间中，变异系数趋近于0
    # 这意味着传统距离度量无法有效区分相似性
```

### 向量化具体应用场景

#### 相似公司发现和投资机会识别
```python
class InvestmentOpportunityFinder:
    def __init__(self, vector_db):
        self.vector_db = vector_db

    def find_undervalued_similar_companies(self, target_company_vector,
                                         valuation_threshold=0.8):
        """发现被低估的相似公司"""

        # 1. 找到财务特征相似的公司
        similar_companies = self.vector_db.query_similar_companies(
            target_vector=target_company_vector,
            n_results=50,
            filters={"year": 2020}
        )

        # 2. 筛选估值较低的公司
        undervalued_opportunities = []

        for company in similar_companies['metadatas']:
            # 计算相对估值
            pe_ratio = company.get('pe_ratio', float('inf'))
            industry_median_pe = company.get('industry_median_pe', pe_ratio)

            relative_valuation = pe_ratio / industry_median_pe

            if relative_valuation < valuation_threshold:
                similarity_score = 1 - similar_companies['distances'][
                    similar_companies['metadatas'].index(company)
                ]

                undervalued_opportunities.append({
                    'ts_code': company['ts_code'],
                    'name': company['name'],
                    'similarity_score': similarity_score,
                    'relative_valuation': relative_valuation,
                    'investment_score': similarity_score * (1 - relative_valuation)
                })

        # 按投资评分排序
        return sorted(undervalued_opportunities,
                     key=lambda x: x['investment_score'], reverse=True)

    def sector_rotation_analysis(self, market_conditions_vector):
        """基于市场条件的板块轮动分析"""

        # 查找在当前市场条件下表现良好的历史案例
        historical_winners = self.vector_db.query_similar_companies(
            target_vector=market_conditions_vector,
            filters={"performance": "outperform"},
            n_results=100
        )

        # 分析行业分布
        sector_performance = {}
        for company in historical_winners['metadatas']:
            sector = company['industry']
            if sector not in sector_performance:
                sector_performance[sector] = []
            sector_performance[sector].append(company['return'])

        # 计算各行业平均表现
        sector_rankings = {}
        for sector, returns in sector_performance.items():
            sector_rankings[sector] = {
                'avg_return': np.mean(returns),
                'win_rate': len([r for r in returns if r > 0]) / len(returns),
                'sample_size': len(returns)
            }

        return sorted(sector_rankings.items(),
                     key=lambda x: x[1]['avg_return'], reverse=True)

#### 风险预警和异常检测
```python
class RiskWarningSystem:
    def __init__(self, vector_db):
        self.vector_db = vector_db
        self.risk_models = self._load_risk_models()

    def detect_financial_anomalies(self, company_vector, company_metadata):
        """检测财务异常"""

        # 1. 找到同行业同规模的公司
        peer_companies = self.vector_db.query_similar_companies(
            target_vector=company_vector,
            filters={
                "industry": company_metadata['industry'],
                "market_cap_range": company_metadata['market_cap_range']
            },
            n_results=20
        )

        # 2. 计算异常分数
        anomaly_scores = []

        for peer in peer_companies['embeddings']:
            peer_vector = np.array(peer)

            # 计算马氏距离（考虑特征间相关性）
            diff = company_vector - peer_vector
            cov_matrix = np.cov(np.array(peer_companies['embeddings']).T)

            try:
                inv_cov = np.linalg.inv(cov_matrix)
                mahalanobis_dist = np.sqrt(diff.T @ inv_cov @ diff)
                anomaly_scores.append(mahalanobis_dist)
            except:
                # 如果协方差矩阵不可逆，使用欧氏距离
                euclidean_dist = np.linalg.norm(diff)
                anomaly_scores.append(euclidean_dist)

        # 3. 判断是否异常
        mean_score = np.mean(anomaly_scores)
        std_score = np.std(anomaly_scores)
        z_score = (anomaly_scores[0] - mean_score) / std_score

        risk_level = "低风险"
        if z_score > 2:
            risk_level = "高风险"
        elif z_score > 1:
            risk_level = "中风险"

        return {
            'risk_level': risk_level,
            'anomaly_score': z_score,
            'peer_comparison': {
                'better_than_peers': (z_score < 0),
                'percentile': stats.norm.cdf(z_score) * 100
            }
        }

    def early_warning_system(self, company_vectors_timeseries):
        """基于时间序列的早期预警"""

        warnings = []

        # 检测趋势变化
        for i in range(1, len(company_vectors_timeseries)):
            current_vector = company_vectors_timeseries[i]
            previous_vector = company_vectors_timeseries[i-1]

            # 计算向量变化幅度
            change_magnitude = np.linalg.norm(current_vector - previous_vector)

            # 查找历史上类似变化的案例
            similar_changes = self.vector_db.query_similar_companies(
                target_vector=current_vector - previous_vector,  # 变化向量
                filters={"change_type": "deterioration"},
                n_results=10
            )

            # 分析历史案例的后续表现
            if similar_changes['metadatas']:
                negative_outcomes = sum(1 for case in similar_changes['metadatas']
                                      if case.get('future_performance', 0) < 0)
                risk_probability = negative_outcomes / len(similar_changes['metadatas'])

                if risk_probability > 0.7:
                    warnings.append({
                        'type': 'trend_deterioration',
                        'risk_probability': risk_probability,
                        'similar_cases': len(similar_changes['metadatas']),
                        'change_magnitude': change_magnitude
                    })

        return warnings
```

#### 特征相似性分析和模式识别
```python
class PatternRecognitionEngine:
    def __init__(self, vector_db):
        self.vector_db = vector_db

    def identify_success_patterns(self, successful_companies_vectors):
        """识别成功公司的共同模式"""

        # 1. 对成功公司进行聚类
        from sklearn.cluster import KMeans

        kmeans = KMeans(n_clusters=5, random_state=42)
        clusters = kmeans.fit_predict(successful_companies_vectors)

        # 2. 分析每个聚类的特征
        success_patterns = []

        for cluster_id in range(5):
            cluster_companies = successful_companies_vectors[clusters == cluster_id]

            if len(cluster_companies) > 0:
                # 计算聚类中心
                cluster_center = np.mean(cluster_companies, axis=0)

                # 找到最具代表性的特征
                feature_importance = np.abs(cluster_center)
                top_features = np.argsort(feature_importance)[-10:]

                # 查找这个模式的历史表现
                pattern_performance = self._analyze_pattern_performance(
                    cluster_center, cluster_companies
                )

                success_patterns.append({
                    'pattern_id': cluster_id,
                    'companies_count': len(cluster_companies),
                    'key_features': top_features,
                    'avg_performance': pattern_performance['avg_return'],
                    'success_rate': pattern_performance['success_rate'],
                    'pattern_vector': cluster_center
                })

        return success_patterns

    def find_emerging_patterns(self, recent_vectors, historical_vectors):
        """发现新兴投资模式"""

        # 使用异常检测找到与历史不同的新模式
        from sklearn.ensemble import IsolationForest

        # 训练异常检测器
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        iso_forest.fit(historical_vectors)

        # 检测最近数据中的异常（可能是新模式）
        anomaly_scores = iso_forest.decision_function(recent_vectors)
        anomalies = recent_vectors[anomaly_scores < -0.1]  # 选择异常样本

        if len(anomalies) > 10:  # 如果有足够的异常样本
            # 对异常样本进行聚类
            from sklearn.cluster import DBSCAN

            clustering = DBSCAN(eps=0.5, min_samples=5)
            clusters = clustering.fit_predict(anomalies)

            emerging_patterns = []
            for cluster_id in set(clusters):
                if cluster_id != -1:  # 排除噪声点
                    cluster_vectors = anomalies[clusters == cluster_id]

                    emerging_patterns.append({
                        'pattern_id': f"emerging_{cluster_id}",
                        'sample_size': len(cluster_vectors),
                        'pattern_center': np.mean(cluster_vectors, axis=0),
                        'novelty_score': np.mean(anomaly_scores[clusters == cluster_id])
                    })

            return emerging_patterns

        return []

#### 实时查询和推荐系统
```python
class RealTimeRecommendationSystem:
    def __init__(self, vector_db):
        self.vector_db = vector_db
        self.user_profiles = {}

    def build_user_investment_profile(self, user_id, historical_investments):
        """构建用户投资偏好向量"""

        investment_vectors = []

        for investment in historical_investments:
            company_vector = self.vector_db.get_company_vector(
                investment['ts_code'], investment['year']
            )

            if company_vector is not None:
                # 根据投资结果加权
                weight = 1.0 if investment['outcome'] == 'positive' else 0.5
                weighted_vector = company_vector * weight
                investment_vectors.append(weighted_vector)

        if investment_vectors:
            # 用户偏好向量 = 历史投资的加权平均
            user_profile_vector = np.mean(investment_vectors, axis=0)
            self.user_profiles[user_id] = user_profile_vector

            return user_profile_vector

        return None

    def real_time_recommendations(self, user_id, market_conditions,
                                 max_recommendations=10):
        """实时投资推荐"""

        if user_id not in self.user_profiles:
            return []

        user_profile = self.user_profiles[user_id]

        # 1. 基于用户偏好找到相似的投资机会
        similar_investments = self.vector_db.query_similar_companies(
            target_vector=user_profile,
            filters={
                "year": 2020,
                "market_conditions": market_conditions
            },
            n_results=max_recommendations * 3  # 获取更多候选
        )

        # 2. 结合当前市场条件进行筛选
        recommendations = []

        for i, company in enumerate(similar_investments['metadatas']):
            similarity_score = 1 - similar_investments['distances'][i]

            # 计算推荐分数
            recommendation_score = self._calculate_recommendation_score(
                company, similarity_score, market_conditions
            )

            recommendations.append({
                'ts_code': company['ts_code'],
                'name': company['name'],
                'similarity_score': similarity_score,
                'recommendation_score': recommendation_score,
                'reasons': self._generate_recommendation_reasons(
                    company, user_profile
                )
            })

        # 3. 按推荐分数排序并返回前N个
        recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)

        return recommendations[:max_recommendations]
```

### 向量化 vs 传统方法

#### 性能对比与适用性
```python
# 性能对比示例
def performance_comparison():
    """向量数据库 vs 传统数据库性能对比"""

    # 传统SQL查询相似公司
    sql_query = """
    SELECT ts_code, name,
           ABS(roe - 0.15) + ABS(roa - 0.08) + ABS(debt_ratio - 0.4) as similarity
    FROM financial_data
    WHERE industry = 'technology'
    ORDER BY similarity
    LIMIT 10
    """
    # 问题：
    # 1. 只能处理少数几个特征
    # 2. 线性组合，无法捕捉复杂关系
    # 3. 需要预定义查询条件
    # 4. 扩展到325个特征时查询会非常复杂

    # 向量数据库查询
    vector_query = """
    vector_db.query(
        target_vector=company_325d_vector,
        n_results=10,
        filters={"industry": "technology"}
    )
    """
    # 优势：
    # 1. 自动处理所有325个特征
    # 2. 捕捉非线性特征关系
    # 3. 亚秒级查询响应
    # 4. 支持复杂的相似性度量
```

### 传统方法可行性

#### 仅使用传统方法的实现路径
```python
class TraditionalSimilarityAnalysis:
    def __init__(self, db_connection):
        self.db = db_connection

    def find_similar_companies_sql(self, target_company_code, top_k=10):
        """使用SQL进行相似性分析"""

        # 1. 获取目标公司的财务指标
        target_query = """
        SELECT roe, roa, debt_ratio, current_ratio, gross_margin,
               revenue_growth_1y, profit_growth_1y, asset_turnover
        FROM financial_ratios
        WHERE ts_code = %s AND year = 2020
        """

        target_metrics = self.db.execute(target_query, (target_company_code,)).fetchone()

        if not target_metrics:
            return []

        # 2. 计算与所有其他公司的相似度
        similarity_query = """
        SELECT ts_code, name, industry,
               (
                   ABS(roe - %s) * 0.2 +
                   ABS(roa - %s) * 0.2 +
                   ABS(debt_ratio - %s) * 0.15 +
                   ABS(current_ratio - %s) * 0.15 +
                   ABS(gross_margin - %s) * 0.1 +
                   ABS(revenue_growth_1y - %s) * 0.1 +
                   ABS(profit_growth_1y - %s) * 0.05 +
                   ABS(asset_turnover - %s) * 0.05
               ) as similarity_score
        FROM financial_ratios fr
        JOIN company_info ci ON fr.ts_code = ci.ts_code
        WHERE fr.year = 2020 AND fr.ts_code != %s
        ORDER BY similarity_score ASC
        LIMIT %s
        """

        params = list(target_metrics) + [target_company_code, top_k]
        results = self.db.execute(similarity_query, params).fetchall()

        return results
```

#### 传统统计学方法
```python
class TraditionalStatisticalAnalysis:
    def __init__(self, financial_data):
        self.data = financial_data

    def correlation_analysis(self, target_returns):
        """相关性分析"""

        # 计算各财务指标与收益率的相关性
        correlations = {}

        financial_metrics = ['roe', 'roa', 'debt_ratio', 'current_ratio',
                           'revenue_growth', 'profit_margin']

        for metric in financial_metrics:
            correlation = np.corrcoef(self.data[metric], target_returns)[0, 1]
            correlations[metric] = correlation

        # 按相关性排序
        sorted_correlations = sorted(correlations.items(),
                                   key=lambda x: abs(x[1]), reverse=True)

        return sorted_correlations
```

#### 基于规则的特征匹配与筛选
```python
class RuleBasedAnalysis:
    def __init__(self):
        self.rules = self._define_investment_rules()

    def _define_investment_rules(self):
        """定义投资规则"""
        return {
            'value_stocks': {
                'pe_ratio': ('<=', 15),
                'pb_ratio': ('<=', 2),
                'debt_ratio': ('<=', 0.5),
                'roe': ('>=', 0.1)
            },
            'growth_stocks': {
                'revenue_growth_3y': ('>=', 0.15),
                'profit_growth_3y': ('>=', 0.2),
                'roe': ('>=', 0.15),
                'debt_ratio': ('<=', 0.4)
            },
            'quality_stocks': {
                'roe': ('>=', 0.15),
                'roa': ('>=', 0.08),
                'current_ratio': ('>=', 1.5),
                'debt_ratio': ('<=', 0.3),
                'profit_margin': ('>=', 0.1)
            }
        }

    def screen_stocks(self, financial_data, strategy='quality_stocks'):
        """股票筛选"""

        if strategy not in self.rules:
            raise ValueError(f"Unknown strategy: {strategy}")

        rules = self.rules[strategy]
        filtered_stocks = financial_data.copy()

        for metric, (operator, threshold) in rules.items():
            if metric in filtered_stocks.columns:
                if operator == '>=':
                    filtered_stocks = filtered_stocks[
                        filtered_stocks[metric] >= threshold
                    ]
                elif operator == '<=':
                    filtered_stocks = filtered_stocks[
                        filtered_stocks[metric] <= threshold
                    ]
                elif operator == '>':
                    filtered_stocks = filtered_stocks[
                        filtered_stocks[metric] > threshold
                    ]
                elif operator == '<':
                    filtered_stocks = filtered_stocks[
                        filtered_stocks[metric] < threshold
                    ]

        return filtered_stocks
```

