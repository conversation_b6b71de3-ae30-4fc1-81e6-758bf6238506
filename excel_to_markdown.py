#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel to Markdown Converter
将docs目录下的所有Excel文件转换为Markdown格式的表格
"""

import os
import pandas as pd
from pathlib import Path
import argparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def excel_to_markdown_table(df, max_rows=None):
    """
    将DataFrame转换为Markdown表格格式
    
    Args:
        df: pandas DataFrame
        max_rows: 最大显示行数，None表示显示所有行
    
    Returns:
        str: Markdown格式的表格字符串
    """
    if df.empty:
        return "表格为空\n\n"
    
    # 如果指定了最大行数，则截取
    if max_rows and len(df) > max_rows:
        df = df.head(max_rows)
        truncated = True
    else:
        truncated = False
    
    # 处理列名，确保不为空
    df.columns = [str(col) if pd.notna(col) else f"列{i+1}" for i, col in enumerate(df.columns)]
    
    # 将所有数据转换为字符串，处理NaN值
    df_str = df.astype(str).replace('nan', '')
    
    # 创建Markdown表格
    markdown_lines = []
    
    # 表头
    header = "| " + " | ".join(df_str.columns) + " |"
    markdown_lines.append(header)
    
    # 分隔线
    separator = "| " + " | ".join(["---"] * len(df_str.columns)) + " |"
    markdown_lines.append(separator)
    
    # 数据行
    for _, row in df_str.iterrows():
        row_str = "| " + " | ".join(row.values) + " |"
        markdown_lines.append(row_str)
    
    result = "\n".join(markdown_lines) + "\n\n"
    
    if truncated:
        result += f"*注：表格已截取前{max_rows}行，原表格共{len(df) + max_rows}行*\n\n"
    
    return result


def convert_excel_file(excel_path, output_dir, max_rows_per_sheet=100):
    """
    转换单个Excel文件为Markdown
    
    Args:
        excel_path: Excel文件路径
        output_dir: 输出目录
        max_rows_per_sheet: 每个工作表最大显示行数
    """
    try:
        logger.info(f"正在处理文件: {excel_path}")
        
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(excel_path)
        
        # 创建输出文件名
        base_name = Path(excel_path).stem
        output_file = Path(output_dir) / f"{base_name}.md"
        
        markdown_content = []
        markdown_content.append(f"# {base_name}\n\n")
        markdown_content.append(f"*源文件: {Path(excel_path).name}*\n\n")
        
        # 处理每个工作表
        for sheet_name in excel_file.sheet_names:
            logger.info(f"  处理工作表: {sheet_name}")
            
            try:
                # 读取工作表
                df = pd.read_excel(excel_path, sheet_name=sheet_name)
                
                # 添加工作表标题
                if len(excel_file.sheet_names) > 1:
                    markdown_content.append(f"## {sheet_name}\n\n")
                
                # 转换为Markdown表格
                table_md = excel_to_markdown_table(df, max_rows_per_sheet)
                markdown_content.append(table_md)
                
            except Exception as e:
                logger.warning(f"  无法处理工作表 {sheet_name}: {str(e)}")
                markdown_content.append(f"## {sheet_name}\n\n")
                markdown_content.append(f"*错误: 无法读取此工作表 - {str(e)}*\n\n")
        
        # 写入Markdown文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("".join(markdown_content))
        
        logger.info(f"  转换完成: {output_file}")
        return True
        
    except Exception as e:
        logger.error(f"处理文件 {excel_path} 时出错: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将Excel文件转换为Markdown表格')
    parser.add_argument('--input-dir', '-i', default='docs', 
                       help='输入目录 (默认: docs)')
    parser.add_argument('--output-dir', '-o', default='docs_markdown', 
                       help='输出目录 (默认: docs_markdown)')
    parser.add_argument('--max-rows', '-r', type=int, default=100,
                       help='每个工作表最大显示行数 (默认: 100)')
    
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    # 检查输入目录是否存在
    if not input_dir.exists():
        logger.error(f"输入目录不存在: {input_dir}")
        return
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    logger.info(f"输出目录: {output_dir}")
    
    # 查找所有Excel文件
    excel_extensions = ['.xlsx', '.xls', '.xlsm']
    excel_files = []
    
    for ext in excel_extensions:
        excel_files.extend(input_dir.glob(f"*{ext}"))
    
    if not excel_files:
        logger.warning(f"在 {input_dir} 目录下未找到Excel文件")
        return
    
    logger.info(f"找到 {len(excel_files)} 个Excel文件")
    
    # 转换每个Excel文件
    success_count = 0
    for excel_file in excel_files:
        if convert_excel_file(excel_file, output_dir, args.max_rows):
            success_count += 1
    
    logger.info(f"转换完成! 成功转换 {success_count}/{len(excel_files)} 个文件")
    logger.info(f"Markdown文件保存在: {output_dir}")


if __name__ == "__main__":
    main()
